using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Timers;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using ZoomableApp.Services;
using ZoomableApp.Models;

namespace ZoomableApp.ViewModels
{
    /// <summary>
    /// ViewModel cho Daily Plan vs Actual Chart (Panel 3 - Left)
    /// Hi<PERSON>n thị kế hoạch vs thực tế hàng ngày dưới dạng half pie chart
    /// </summary>
    public class DailyPlanActualChartViewModel : INotifyPropertyChanged
    {
        private readonly MockDashboardDataService _mockDataService;
        private System.Timers.Timer _refreshTimer;
        private string _lastUpdated = "";

        public event PropertyChangedEventHandler? PropertyChanged;

        public ObservableCollection<ISeries> Series { get; set; } = new();
        
        public string LastUpdated
        {
            get => _lastUpdated;
            set
            {
                _lastUpdated = value;
                OnPropertyChanged(nameof(LastUpdated));
            }
        }

        private int _planQuantity;
        public int PlanQuantity
        {
            get => _planQuantity;
            set
            {
                if (_planQuantity != value)
                {
                    _planQuantity = value;
                    OnPropertyChanged(nameof(PlanQuantity));
                }
            }
        }

        private int _actualQuantity;
        public int ActualQuantity
        {
            get => _actualQuantity;
            set
            {
                if (_actualQuantity != value)
                {
                    _actualQuantity = value;
                    OnPropertyChanged(nameof(ActualQuantity));
                }
            }
        }

        private int _gap;
        public int Gap
        {
            get => _gap;
            set
            {
                if (_gap != value)
                {
                    _gap = value;
                    OnPropertyChanged(nameof(Gap));
                }
            }
        }

        private double _achievementRate;
        public double AchievementRate
        {
            get => _achievementRate;
            set
            {
                if (_achievementRate != value)
                {
                    _achievementRate = value;
                    OnPropertyChanged(nameof(AchievementRate));
                }
            }
        }

        public string CurrentDay { get; private set; } = "";

        public DailyPlanActualChartViewModel()
        {
            _mockDataService = new MockDashboardDataService();
            CurrentDay = DateTime.Now.ToString("dd/MM/yyyy");
            InitializeTimer();
            LoadDataAsync();
        }

        private void InitializeTimer()
        {
            var interval = ConfigurationService.GetDashboardRefreshInterval();
            _refreshTimer = new System.Timers.Timer(interval);
            _refreshTimer.Elapsed += async (s, e) => await LoadDataAsync();
            _refreshTimer.Start();
        }

        private async Task LoadDataAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    // Kiểm tra PLC mode
                    var plcMode = ConfigurationService.GetPlcMode();
                    
                    if (plcMode == PlcMode.Mock)
                    {
                        LoadMockData();
                    }
                    else
                    {
                        // TODO: Load real data from PLC/Database
                        // Fallback to mock data for now
                        LoadMockData();
                    }

                    LastUpdated = DateTime.Now.ToString("HH:mm:ss");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"DailyPlanActualChart: Error loading data: {ex.Message}");
                    LoadMockData(); // Fallback
                }
            });
        }

        private void LoadMockData()
        {
            var planData = _mockDataService.GetMockPlanActualData(false); // Daily data
            
            PlanQuantity = planData.Plan;
            ActualQuantity = planData.Actual;
            Gap = planData.Gap;
            AchievementRate = PlanQuantity > 0 ? Math.Round((double)ActualQuantity / PlanQuantity * 100, 1) : 0;

            Series.Clear();

            // Tạo half pie chart cho Plan vs Actual
            if (ActualQuantity <= PlanQuantity)
            {
                // Actual không vượt plan
                Series.Add(new PieSeries<int>
                {
                    Values = new[] { ActualQuantity },
                    Name = $"Thực tế ({ActualQuantity})",
                    Fill = new SolidColorPaint(SKColors.LightGreen),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    InnerRadius = 40,
                    DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => $"{point.PrimaryValue}"
                });

                var remaining = PlanQuantity - ActualQuantity;
                if (remaining > 0)
                {
                    Series.Add(new PieSeries<int>
                    {
                        Values = new[] { remaining },
                        Name = $"Còn lại ({remaining})",
                        Fill = new SolidColorPaint(SKColors.LightGray),
                        Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                        InnerRadius = 40,
                        DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                        DataLabelsSize = 12,
                        DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                        DataLabelsFormatter = point => $"{point.PrimaryValue}"
                    });
                }
            }
            else
            {
                // Actual vượt plan
                Series.Add(new PieSeries<int>
                {
                    Values = new[] { PlanQuantity },
                    Name = $"Kế hoạch ({PlanQuantity})",
                    Fill = new SolidColorPaint(SKColors.LightBlue),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    InnerRadius = 40,
                    DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => $"{point.PrimaryValue}"
                });

                var excess = ActualQuantity - PlanQuantity;
                Series.Add(new PieSeries<int>
                {
                    Values = new[] { excess },
                    Name = $"Vượt KH (+{excess})",
                    Fill = new SolidColorPaint(SKColors.Gold),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    InnerRadius = 40,
                    DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => $"+{point.PrimaryValue}"
                });
            }

            System.Diagnostics.Debug.WriteLine($"DailyPlanActual: Plan={PlanQuantity}, Actual={ActualQuantity}, Gap={Gap}, Rate={AchievementRate:F1}%");
        }

        /// <summary>
        /// Force refresh data
        /// </summary>
        public async Task RefreshAsync()
        {
            await LoadDataAsync();
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Dispose()
        {
            _refreshTimer?.Stop();
            _refreshTimer?.Dispose();
        }
    }
}
