﻿using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices; // For CallerMemberName
using System.Windows.Threading;      // For DispatcherTimer
using ZoomableApp.Models;
using ZoomableApp.Layouts;
using ZoomableApp.SharedControls;
using ZoomableApp.Services;
using ZoomableApp.PLC;
using ZoomableApp.ViewModels;
using System.Diagnostics;
using System.Windows.Media.Animation;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using SkiaSharp;
using LiveChartsCore.SkiaSharpView.Painting;



namespace ZoomableApp
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        private Point _panStartPoint;
        private Point _panStartTranslate;
        private bool _isPanning;
        private ToastNotificationPanel _toastNotification;
        private PlcConnectionManager _plcManager;
        private DataAggregatorService _dataAggregator;
        private IDatabaseService _databaseService;
        private ProductionDataService _productionDataService;
        private DispatcherTimer _plcDataReaderTimer; // Timer để đọc dữ liệu từ PLC định kỳ
        private DispatcherTimer _shiftChangeTimer; // Timer để kiểm tra chuyển ca
        private HomeDailyPlanViewModel _dailyPlanViewModel;
        private PlanViewModel _planViewModel;
        private ILoggerService _logger;
        private DashboardDataService _dashboardDataService;
        private PlcFaultService _faultService;
        private CanvasFaultDisplayService _canvasFaultDisplayService;

        // Dashboard Chart ViewModels (now handled by individual controls)
        private DateTime _lastShiftChangeCheck = DateTime.MinValue;

        public ObservableCollection<PlcConnectionUIData> PlcConnections { get; set; }
        public PlanViewModel SharedPlanViewModel => _planViewModel;

        public ICommand ConnectPlcCommand { get; private set; }
        public ICommand DisconnectPlcCommand { get; private set; }
        public ICommand WriteToPlcCommand { get; private set; }
        public ICommand ReadFromPlcCommand { get; private set; }

        private const double ZoomSpeed = 1.1;
        private const double MaxZoom = 5.0;
        private const double MinZoom = 0.2;

        // Instances of the layouts to manage event subscriptions and product transfer
        private MainlineLayout _mainlineLayoutInstance;
        private InspectionLayout _inspectionLayoutInstance;
        // A general reference to the currently loaded UserControl layout
        private UserControl _currentLoadedLayout;

        private ObservableCollection<ErrorNotification> _allErrors = new ObservableCollection<ErrorNotification>();
        public ObservableCollection<ErrorNotification> AllErrors
        {
            get => _allErrors;
            set { _allErrors = value; OnPropertyChanged(); }
        }
        private int _newErrorCount;
        public int NewErrorCount
        {
            get => _newErrorCount;
            set { _newErrorCount = value; OnPropertyChanged(); OnPropertyChanged(nameof(HasNewErrors)); }
        }

        public bool HasNewErrors => NewErrorCount > 0;

        private bool _isErrorListPopupOpen;
        public bool IsErrorListPopupOpen
        {
            get => _isErrorListPopupOpen;
            set { _isErrorListPopupOpen = value; OnPropertyChanged(); }
        }

        private string _valueToWrite = "123"; // Giá trị mặc định để ghi
        public string ValueToWrite
        {
            get => _valueToWrite;
            set { _valueToWrite = value; OnPropertyChanged(); }
        }

        private string _readValue = ""; // Giá trị đọc được (hiển thị)
        public string ReadValue
        {
            get => _readValue;
            set { _readValue = value; OnPropertyChanged(); }
        }
        private bool _isSidebarOpen = false;
        public bool IsSidebarOpen
        {
            get => _isSidebarOpen;
            set { _isSidebarOpen = value; OnPropertyChanged(); }
        }

        // Properties for Daily Plan section bindings
        public string TodayDateText => _dailyPlanViewModel?.TodayDateText ?? "Đang tính...";
        public string CurrentProductText => _dailyPlanViewModel?.CurrentProductText ?? "Đang tải...";
        public ObservableCollection<ViewModels.DailyPlanItem> DailyPlanItems
        {
            get
            {
                System.Diagnostics.Debug.WriteLine("[MainWindow] UI is requesting DailyPlanItems. Returning HomeVM's collection.");
                return _dailyPlanViewModel?.DailyPlanItems;
            }
        }

        public object SelectedItem
        {
            get => _dailyPlanViewModel?.SelectedItem;
            set { if (_dailyPlanViewModel != null) _dailyPlanViewModel.SelectedItem = value as ViewModels.DailyPlanItem; }
        }

        // Commands for Daily Plan
        public ICommand RefreshCommand => _dailyPlanViewModel?.RefreshCommand;
        public ICommand MarkCompleteCommand => _dailyPlanViewModel?.MarkCompleteCommand;
        public ICommand StartNextCommand => _dailyPlanViewModel?.StartNextCommand;
        public ICommand StartEditModelCommand => _dailyPlanViewModel?.StartEditModelCommand;
        public ICommand SaveModelCommand => _dailyPlanViewModel?.SaveModelCommand;
        public ICommand CancelEditModelCommand => _dailyPlanViewModel?.CancelEditModelCommand;

        // Properties for Model Editing
        public string EditableModelName
        {
            get => _dailyPlanViewModel?.EditableModelName ?? "";
            set { if (_dailyPlanViewModel != null) _dailyPlanViewModel.EditableModelName = value; }
        }

        public bool IsEditingModel => _dailyPlanViewModel?.IsEditingModel ?? false;

        // Properties for PlanPage bindings
        public string ExcelFilePath => _planViewModel?.ExcelFilePath ?? "";
        public string ExcelFileStatus => _planViewModel?.ExcelFileStatus ?? "";
        public string ExcelRowCount => _planViewModel?.ExcelRowCount ?? "";
        public string LastUpdated => _planViewModel?.LastUpdated ?? "";
        public string PlanStatus => _planViewModel?.PlanStatus ?? "";

        // Dashboard data properties for charts
        private string _todayTotalIdleTime = "0:00";
        public string TodayTotalIdleTime
        {
            get => _todayTotalIdleTime;
            set { _todayTotalIdleTime = value; OnPropertyChanged(); }
        }

        private string _todayRemainingIdleTime = "2:00";
        public string TodayRemainingIdleTime
        {
            get => _todayRemainingIdleTime;
            set { _todayRemainingIdleTime = value; OnPropertyChanged(); }
        }

        private string _monthTotalIdleTime = "0:00";
        public string MonthTotalIdleTime
        {
            get => _monthTotalIdleTime;
            set { _monthTotalIdleTime = value; OnPropertyChanged(); }
        }

        private string _monthRemainingIdleTime = "40:00";
        public string MonthRemainingIdleTime
        {
            get => _monthRemainingIdleTime;
            set { _monthRemainingIdleTime = value; OnPropertyChanged(); }
        }
        public System.Data.DataTable MonthlyPlanData => _planViewModel?.MonthlyPlanData;
        public System.Data.DataTable DailyPlanData => _planViewModel?.DailyPlanData;
        public ObservableCollection<ISeries> Series { get; set; }
        double limitTime = 100;
        double remainingTime = 30;
        public SolidColorPaint LegendPaint { get; } = new SolidColorPaint(SKColors.White);

        // private DispatcherTimer _plcErrorSimulatorTimer; //phục vụ mô phỏng lỗi
        //private int _simulatedErrorCounter = 0; // phục vụ mô phỏng lỗi
        //private Random _random = new Random(); // phục vụ mô phỏng lỗi

        public MainWindow()
        {
            InitializeComponent();
            DataContext = this; // Set DataContext for bindings to MainWindow properties

            PieChart.LegendTextPaint = new SolidColorPaint(SKColors.White);
            PieChart2.LegendTextPaint = new SolidColorPaint(SKColors.White);

            Wpf.Ui.Appearance.ApplicationThemeManager.Apply(
              Wpf.Ui.Appearance.ApplicationTheme.Light, // Theme type
              Wpf.Ui.Controls.WindowBackdropType.Mica,  // Background type
              true                                      // Whether to change accents automatically
            );
            // Animation for the loop gradient brush
            var brush1 = (VisualBrush)Application.Current.Resources["LoopGradientBrush1"];
            var brush2 = (VisualBrush)Application.Current.Resources["LoopGradientBrush2"];
            var anim = new RectAnimation
            {
                From = new Rect(0, 0, 1, 1),
                To = new Rect(0, 1, 1, 1),
                Duration = new Duration(TimeSpan.FromSeconds(2)),
                RepeatBehavior = RepeatBehavior.Forever
            };
            brush1.BeginAnimation(VisualBrush.ViewportProperty, anim, HandoffBehavior.Compose);
            brush2.BeginAnimation(VisualBrush.ViewportProperty, anim, HandoffBehavior.Compose);

            // Bypass đăng nhập - hiển thị thông tin user mặc định
            UserFullNameTextBlock.Text = "Admin User";
            UserShiftTextBlock.Text = "Ca hành chính: 08:00-17:00";

            // Update user info and menu visibility
            UpdateUserInfo();

            // Khởi tạo essential services với error handling
            InitializeEssentialServices();

            // Load layout based on configuration
            var defaultLayout = ConfigurationService.GetDefaultLayout();
            LoadLayout(defaultLayout);
            CurrentLayoutTextBlock.Text = $"Layout: {defaultLayout}";

            SidebarPanel.Visibility = Visibility.Collapsed;
            SidebarColumn.Width = new GridLength(0, GridUnitType.Pixel); // Thu gọn hoàn toàn

            this.Closing += MainWindow_Closing;

            var elapsedTime = limitTime - remainingTime;

            Series = new ObservableCollection<ISeries>
            {
                new PieSeries<double>
                {
                    Values = new[] { elapsedTime },
                    Name = "Đã dùng",
                    InnerRadius = 50,
                    MaxRadialColumnWidth = 50,
                },
                new PieSeries<double>
                {
                    Values = new[] { remainingTime },
                    Name = "Còn lại",
                    InnerRadius = 50,
                    MaxRadialColumnWidth = 50
                }
            };

            // Lazy load các services nặng trong background
            _ = Task.Run(async () =>
            {
                await InitializeLazyServices();
            });
        }

        private void InitializeEssentialServices()
        {
            try
            {
                // Khởi tạo Service Container với PLC mode từ config
                var plcMode = ConfigurationService.GetPlcMode();
                ServiceContainer.Initialize(plcMode);

                // Get logger service
                _logger = ServiceContainer.GetService<ILoggerService>();
                _logger.LogInfo("MainWindow: Essential services initialization started");

                // Khởi tạo PLC Manager với error handling
                var loadedPlcConfigs = ConfigLoader.LoadPlcConfigs();
                _plcManager = new PlcConnectionManager(loadedPlcConfigs, plcMode);
                PlcConnections = new ObservableCollection<PlcConnectionUIData>();

                //Đăng kí sự kiện cho plc manager
                _plcManager.PlcConnectionFailed += PlcManager_PlcConnectionFailed;

                //Cập nhật UI (PlcConnections là ObservableCollection<PlcConnectionUIData>)
                foreach (var configInfo in _plcManager.GetAllPlcConnectionInfos())
                {
                    PlcConnections.Add(new PlcConnectionUIData(configInfo));
                }
            }
            catch (Exception ex)
            {
                // Nếu có lỗi config, tạo PLC Manager rỗng với Mock mode
                System.Diagnostics.Debug.WriteLine($"Error loading PLC configs: {ex.Message}");
                var plcMode = PlcMode.Mock; // Fallback to Mock mode on error
                ServiceContainer.Initialize(plcMode);

                // Get logger service for error logging
                _logger = ServiceContainer.GetService<ILoggerService>();
                _logger.LogError("Error loading PLC configs, falling back to Mock mode", ex);

                var emptyConfigs = new List<PlcConnectionInfo>();
                _plcManager = new PlcConnectionManager(emptyConfigs, plcMode);
                PlcConnections = new ObservableCollection<PlcConnectionUIData>();
                _plcManager.PlcConnectionFailed += PlcManager_PlcConnectionFailed;
            }

            // Khởi tạo toast notification
            InitializeToastNotification();

            // Khởi tạo commands
            ConnectPlcCommand = new RelayCommand<string>(async (plcId) => await ExecuteConnectPlc(plcId), CanExecuteConnectPlc);
            DisconnectPlcCommand = new RelayCommand<string>(async (plcId) => await ExecuteDisconnectPlc(plcId), CanExecuteDisconnectPlc);
            WriteToPlcCommand = new RelayCommand<PlcConnectionUIData>(async (plcUiData) => await ExecuteWriteToPlc(plcUiData), CanExecuteReadWritePlc);
            ReadFromPlcCommand = new RelayCommand<PlcConnectionUIData>(async (plcUiData) => await ExecuteReadFromPlc(plcUiData), CanExecuteReadWritePlc);
        }

        private async Task InitializeLazyServices()
        {
            try
            {
                await Task.Delay(500); // Đợi UI load xong

                // Khởi tạo database services
                Dispatcher.Invoke(() =>
                {
                    _dataAggregator = new DataAggregatorService();
                    _databaseService = new SimulatedDatabaseService();
                    _dataAggregator.RecordCompleted += DataAggregator_RecordCompleted;

                    // Khởi tạo ProductionDataService cho báo cáo
                    _productionDataService = new ProductionDataService(_plcManager);
                });

                // Initialize database async
                await _productionDataService.InitializeDatabaseAsync();

                // Khởi tạo timers
                Dispatcher.Invoke(() =>
                {
                    InitializePlcDataReaderTimer();
                    UpdatePlcDataReaderTimerState();
                    InitializeShiftChangeTimer();

                    // Initialize shared ViewModels
                    _planViewModel = new PlanViewModel();
                    _dailyPlanViewModel = new HomeDailyPlanViewModel(_planViewModel);

                    // Get dashboard service from container
                    _dashboardDataService = ServiceContainer.GetService<DashboardDataService>();

                    // Get fault service from container
                    _faultService = ServiceContainer.GetService<PlcFaultService>();
                    _canvasFaultDisplayService = ServiceContainer.GetService<CanvasFaultDisplayService>();

                    // Initialize dashboard chart ViewModels
                    InitializeDashboardCharts();
                    OnPropertyChanged(nameof(DailyPlanItems));
                    DailyPlanSection.DataContext = _dailyPlanViewModel;

                    // Subscribe to ViewModel property changes to update MainWindow bindings
                    _planViewModel.PropertyChanged += (s, e) => {
                        System.Diagnostics.Debug.WriteLine($"[MainWindow] Received PropertyChanged from PlanVM for: {e.PropertyName}");
                        NotifyMainWindowPropertyChanges();
                    };
                    _dailyPlanViewModel.PropertyChanged += (s, e) => {
                        System.Diagnostics.Debug.WriteLine($"[MainWindow] Received PropertyChanged from HomeVM for: {e.PropertyName}");
                        NotifyMainWindowPropertyChanges();
                    };

                    // Set up page DataContexts
                    SetupPageDataContexts();

                    // Initial property change notification
                    NotifyMainWindowPropertyChanges();
                });

                // Load dashboard data
                await LoadDashboardDataAsync();

                // Auto-connect PLCs
                await Task.Delay(1000);
                await AutoConnectPlcsOnStartup();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in lazy services initialization: {ex.Message}");
                _logger?.LogError("Error in lazy services initialization", ex);
            }
        }

        private void SetupPageDataContexts()
        {
            try
            {
                // Set DataContext for PlanPage
                var planPageContent = ((ContentControl)PlanPage).Content as Views.PlanPage;
                if (planPageContent != null)
                {
                    planPageContent.DataContext = _planViewModel;
                }

                // Set up ReportPage with ProductionDataService
                var reportPageContent = ((ContentControl)ReportPage).Content as Views.ReportPage;
                if (reportPageContent != null)
                {
                    reportPageContent.DataContext = new ViewModels.ReportViewModel(_productionDataService);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error setting up page DataContexts: {ex.Message}");
                _logger?.LogError("Error setting up page DataContexts", ex);
            }
        }

        private void NotifyMainWindowPropertyChanges()
        {
            OnPropertyChanged(nameof(TodayDateText));
            OnPropertyChanged(nameof(CurrentProductText));
            OnPropertyChanged(nameof(DailyPlanItems));
            OnPropertyChanged(nameof(SelectedItem));
            OnPropertyChanged(nameof(RefreshCommand));
            OnPropertyChanged(nameof(MarkCompleteCommand));
            OnPropertyChanged(nameof(StartNextCommand));
            OnPropertyChanged(nameof(StartEditModelCommand));
            OnPropertyChanged(nameof(SaveModelCommand));
            OnPropertyChanged(nameof(CancelEditModelCommand));
            OnPropertyChanged(nameof(EditableModelName));
            OnPropertyChanged(nameof(IsEditingModel));
            OnPropertyChanged(nameof(ExcelFilePath));
            OnPropertyChanged(nameof(ExcelFileStatus));
            OnPropertyChanged(nameof(ExcelRowCount));
            OnPropertyChanged(nameof(LastUpdated));
            OnPropertyChanged(nameof(PlanStatus));
            OnPropertyChanged(nameof(MonthlyPlanData));
            OnPropertyChanged(nameof(DailyPlanData));
        }

        // Debug event handlers for Daily Plan buttons
        private void RefreshPlanButton_Click(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("RefreshPlanButton clicked!");
        }

        private void MarkCompleteButton_Click(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("MarkCompleteButton clicked!");
        }

        private void StartNextButton_Click(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("StartNextButton clicked!");
        }

        private void OpenSidebarButton_Click(object sender, RoutedEventArgs e)
        {
            IsSidebarOpen = true;
            AnimateSidebar(true);
        }

        private void CloseSidebarButton_Click(object sender, RoutedEventArgs e)
        {
            IsSidebarOpen = false;
            AnimateSidebar(false);
        }

        private void SidebarMenuListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (SidebarMenuListBox.SelectedItem is ListBoxItem selectedItem)
            {
                string menuText = selectedItem.Content.ToString();
                System.Diagnostics.Debug.WriteLine($"Menu selected: '{menuText}'");
                SwitchToPage(menuText);

                // Đóng sidebar sau khi chọn menu (tùy chọn)
                IsSidebarOpen = false;
                AnimateSidebar(false);
            }
        }

        private void SwitchToPage(string pageName)
        {
            System.Diagnostics.Debug.WriteLine($"SwitchToPage called with: '{pageName}'");

            // Ẩn tất cả các trang
            ZoomPanPage.Visibility = Visibility.Collapsed;
            PlanPage.Visibility = Visibility.Collapsed;
            ReportPage.Visibility = Visibility.Collapsed;
            MaintenancePage.Visibility = Visibility.Collapsed;
            SettingsPage.Visibility = Visibility.Collapsed;
            UsersPage.Visibility = Visibility.Collapsed;

            // Xử lý text menu (loại bỏ emoji và khoảng trắng thừa)
            string cleanPageName = pageName?.Trim();
            if (cleanPageName != null)
            {
                // Loại bỏ emoji ở đầu bằng cách tìm khoảng trắng đầu tiên
                int spaceIndex = cleanPageName.IndexOf(' ');
                if (spaceIndex > 0)
                {
                    cleanPageName = cleanPageName.Substring(spaceIndex + 1).Trim();
                }
            }

            System.Diagnostics.Debug.WriteLine($"Clean page name: '{cleanPageName}'");

            // Hiển thị trang được chọn và cập nhật tiêu đề
            switch (cleanPageName)
            {
                case "Trang chủ":
                    ZoomPanPage.Visibility = Visibility.Visible;
                    UpdatePageTitle("Trang chủ");
                    break;
                case "Kế hoạch":
                    PlanPage.Visibility = Visibility.Visible;
                    UpdatePageTitle("Kế hoạch");
                    break;
                case "Báo cáo":
                    ReportPage.Visibility = Visibility.Visible;
                    UpdatePageTitle("Báo cáo");
                    break;
                case "Bảo dưỡng":
                    MaintenancePage.Visibility = Visibility.Visible;
                    UpdatePageTitle("Bảo dưỡng");
                    break;
                case "Cài đặt":
                    SettingsPage.Visibility = Visibility.Visible;
                    UpdatePageTitle("Cài đặt");
                    break;
                case "Người dùng":
                    UsersPage.Visibility = Visibility.Visible;
                    UpdatePageTitle("Quản lý người dùng");
                    break;
                default:
                    ZoomPanPage.Visibility = Visibility.Visible; // Mặc định về trang chủ
                    UpdatePageTitle("Trang chủ");
                    System.Diagnostics.Debug.WriteLine($"Unknown page name: '{cleanPageName}', defaulting to Trang chủ");
                    break;
            }
        }

        private void UpdatePageTitle(string title)
        {
            if (PageTitleTextBlock != null)
            {
                PageTitleTextBlock.Text = title;
                System.Diagnostics.Debug.WriteLine($"Page title updated to: '{title}'");
            }
        }

        private void AnimateSidebar(bool open)
        {
            // Kích thước mong muốn của sidebar
            double targetWidth = open ? 200 : 0; // 200px khi mở, 0px khi đóng
            Duration duration = new Duration(TimeSpan.FromSeconds(0.3));

            if (open)
            {
                SidebarPanel.Visibility = Visibility.Visible;
                // Set width ngay lập tức để có thể animate
                SidebarColumn.Width = new GridLength(targetWidth, GridUnitType.Pixel);

                // Animate opacity từ 0 đến 1
                var opacityAnimation = new DoubleAnimation
                {
                    From = 0,
                    To = 1,
                    Duration = duration,
                    EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
                };

                SidebarPanel.BeginAnimation(UIElement.OpacityProperty, opacityAnimation);
            }
            else
            {
                // Animate opacity từ 1 đến 0
                var opacityAnimation = new DoubleAnimation
                {
                    From = 1,
                    To = 0,
                    Duration = duration,
                    EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseIn }
                };

                opacityAnimation.Completed += (s, e) =>
                {
                    SidebarPanel.Visibility = Visibility.Collapsed;
                    SidebarColumn.Width = new GridLength(0, GridUnitType.Pixel);
                };

                SidebarPanel.BeginAnimation(UIElement.OpacityProperty, opacityAnimation);
            }
        }

        private bool CanExecuteReadWritePlc(PlcConnectionUIData plcUiData)
        {
            return plcUiData != null && plcUiData.IsConnected;
        }

        private async Task ExecuteWriteToPlc(PlcConnectionUIData plcUiData)
        {
            if (plcUiData == null || !plcUiData.IsConnected)
            {
                AddNewError(plcUiData?.PlcId ?? "UnknownPLC", "Cannot write: PLC not connected.");
                return;
            }

            var plcService = _plcManager.GetPlcService(plcUiData.PlcId);
            if (plcService == null)
            {
                AddNewError(plcUiData.PlcId, "PLC service not found for writing.");
                return;
            }

            // Sử dụng PlcDeviceAddress đã định nghĩa cho thanh ghi test
            PlcDeviceAddress targetDeviceAddress = PlcDeviceAddress.BitTest;
            var regInfo = plcService.GetRegisterInfo(targetDeviceAddress); // Lấy thông tin thanh ghi (bao gồm DataType)

            object valueToActuallyWrite = null;
            try
            {
                if (regInfo.DataType == PlcDataType.WORD)
                {
                    valueToActuallyWrite = short.Parse(plcUiData.ValueToWrite);
                }
                else if (regInfo.DataType == PlcDataType.BIT)
                {
                    bool successfullyParsedBit = false;
                    if (bool.TryParse(plcUiData.ValueToWrite, out bool bVal))
                    {
                        valueToActuallyWrite = bVal;
                        successfullyParsedBit = true;
                        System.Diagnostics.Debug.WriteLine($"Parsed BIT value: {bVal}"); // Debug log
                    }
                    else if (int.TryParse(plcUiData.ValueToWrite, out int iValForBit)) // Đổi tên biến để rõ ràng
                    {
                        if (iValForBit == 0 || iValForBit == 1)
                        {
                            valueToActuallyWrite = (iValForBit != 0);
                            successfullyParsedBit = true;
                        }
                    }

                    if (!successfullyParsedBit)
                    {
                        throw new FormatException("Invalid value for BIT. Expected 'true', 'false', '0', or '1'.");
                    }
                }
                // Thêm các kiểu khác nếu TestData có thể thay đổi kiểu
                else if (regInfo.DataType == PlcDataType.DWORD)
                {
                    valueToActuallyWrite = int.Parse(plcUiData.ValueToWrite);
                }
                else if (regInfo.DataType == PlcDataType.FLOAT)
                {
                    valueToActuallyWrite = float.Parse(plcUiData.ValueToWrite);
                }
                else
                {
                    AddNewError(plcUiData.PlcId, $"Writing to {targetDeviceAddress} with DataType {regInfo.DataType} is not fully handled in this example. Value: {plcUiData.ValueToWrite}");
                    return;
                }
            }
            catch (Exception ex)
            {
                AddNewError(plcUiData.PlcId, $"Invalid value format '{plcUiData.ValueToWrite}' for {regInfo.DataType}. Error: {ex.Message}");
                return;
            }

            PlcWriteResult result = await plcService.WriteAsync(targetDeviceAddress, valueToActuallyWrite);

            if (result.IsSuccess)
            {
                plcUiData.ReadValue = $"Write D0 OK: {valueToActuallyWrite}";
            }
            else
            {
                AddNewError(plcUiData.PlcId, $"Failed to write to D0 ({targetDeviceAddress}) on {plcUiData.Name}. Error: {result.Message}");
                plcUiData.ReadValue = $"Write D0 Fail: {result.Message}";
            }
            CommandManager.InvalidateRequerySuggested();
        }

        private async Task ExecuteReadFromPlc(PlcConnectionUIData plcUiData)
        {
            if (plcUiData == null || !plcUiData.IsConnected)
            {
                AddNewError(plcUiData?.PlcId ?? "UnknownPLC", "Cannot read: PLC not connected.");
                return;
            }

            var plcService = _plcManager.GetPlcService(plcUiData.PlcId);
            if (plcService == null)
            {
                AddNewError(plcUiData.PlcId, "PLC service not found for reading.");
                return;
            }

            PlcDeviceAddress targetDeviceAddress = PlcDeviceAddress.BitTest;
            PlcReadResult result = await plcService.ReadAsync(targetDeviceAddress);

            if (result.IsSuccess)
            {
                var regInfo = plcService.GetRegisterInfo(targetDeviceAddress);
                string displayValue;
                if (result.Value != null)
                {
                    // Map sang kiểu cụ thể nếu cần, hoặc dùng ToString()
                    if (regInfo.DataType == PlcDataType.WORD && result.Value is short sVal) displayValue = sVal.ToString();
                    else if (regInfo.DataType == PlcDataType.BIT && result.Value is bool bVal) displayValue = bVal.ToString();
                    else if (regInfo.DataType == PlcDataType.DWORD && result.Value is int iVal) displayValue = iVal.ToString();
                    else if (regInfo.DataType == PlcDataType.FLOAT && result.Value is float fVal) displayValue = fVal.ToString("F2");
                    else if (regInfo.DataType == PlcDataType.STRING && result.Value is string strVal) displayValue = strVal.TrimEnd('\0'); // Loại bỏ ký tự null
                    else displayValue = result.Value.ToString(); // Mặc định
                }
                else
                {
                    displayValue = "NULL";
                }
                plcUiData.ReadValue = displayValue;
            }
            else
            {
                AddNewError(plcUiData.PlcId, $"Failed to read from D0 ({targetDeviceAddress}) on {plcUiData.Name}. Error: {result.Message}");
                plcUiData.ReadValue = $"Read D0 Fail: {result.Message}";
            }
            CommandManager.InvalidateRequerySuggested();
        }

        private void PlcManager_PlcConnectionFailed(object sender, PlcErrorEventArgs e)
        {
            // Đảm bảo chạy trên UI thread nếu event được kích hoạt từ thread khác
            Dispatcher.Invoke(() =>
            {
                AddNewError(e.PlcId, e.ErrorMessage); // Gọi AddNewError để hiển thị toast và notification
            });
        }

        private void DataAggregator_RecordCompleted(object sender, ProductRecordEventArgs e)
        {
            Debug.WriteLine($"MainWindow: Received completed record from Aggregator: {e.Record}");
            _logger?.LogInfo($"MainWindow: Received completed record from Aggregator: {e.Record}");
            // Gọi service để lưu vào DB
            _databaseService.SaveProductRecordAsync(e.Record); // Có thể await nếu cần xử lý kết quả
        }

        private async Task AutoConnectPlcsOnStartup()
        {
            if (_plcManager != null)
            {
                await _plcManager.ConnectAllAutoConnectPlcsAsync();
                // Sau khi kết nối xong, cập nhật lại UI
                RefreshPlcConnectionStatusUI();
                UpdatePlcDataReaderTimerState(); // Cập nhật trạng thái timer sau khi auto-connect
            }
        }

        // Phương thức mới để làm mới trạng thái UI từ manager
        private void RefreshPlcConnectionStatusUI()
        {
            if (_plcManager == null || PlcConnections == null) return;

            foreach (var uiData in PlcConnections)
            {
                var serviceInfo = _plcManager.GetPlcConnectionInfo(uiData.PlcId);
                if (serviceInfo != null)
                {
                    uiData.UpdateFromServiceInfo(serviceInfo);
                }
            }
        }
        private async Task ExecuteConnectPlc(string plcId)
        {
            if (string.IsNullOrEmpty(plcId)) return;
            var uiData = PlcConnections.FirstOrDefault(p => p.PlcId == plcId);
            if (uiData == null) return;

            // Lấy config từ manager, không cần cập nhật từ UI nữa vì đã load từ file
            var configToConnect = _plcManager.GetPlcConnectionInfo(plcId);
            if (configToConnect == null)
            {
                uiData.ConnectionStatus = "Error: Config not found.";
                return;
            }

            uiData.ConnectionStatus = "Connecting...";
            bool success = await _plcManager.ConnectPlcAsync(plcId); // Manager sẽ dùng config đã load

            var updatedInfo = _plcManager.GetPlcConnectionInfo(plcId);
            if (updatedInfo != null) uiData.UpdateFromServiceInfo(updatedInfo);
            UpdatePlcDataReaderTimerState();
            CommandManager.InvalidateRequerySuggested();
        }
        private bool CanExecuteConnectPlc(string plcId)
        {
            if (string.IsNullOrEmpty(plcId)) return false;
            var plcInfo = _plcManager?.GetPlcConnectionInfo(plcId);
            return plcInfo != null && !plcInfo.IsConnected;
        }


        private async Task ExecuteDisconnectPlc(string plcId)
        {
            if (string.IsNullOrEmpty(plcId)) return;
            var uiData = PlcConnections.FirstOrDefault(p => p.PlcId == plcId);
            if (uiData != null) uiData.ConnectionStatus = "Disconnecting...";

            await _plcManager.DisconnectPlcAsync(plcId);

            var updatedInfo = _plcManager.GetPlcConnectionInfo(plcId);
            if (updatedInfo != null && uiData != null) uiData.UpdateFromServiceInfo(updatedInfo);
            UpdatePlcDataReaderTimerState();
        }
        private bool CanExecuteDisconnectPlc(string plcId)
        {
            if (string.IsNullOrEmpty(plcId)) return false;
            var plcInfo = _plcManager?.GetPlcConnectionInfo(plcId);
            return plcInfo != null && plcInfo.IsConnected;
        }

        private void InitializePlcDataReaderTimer()
        {
            _plcDataReaderTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5) // Đọc PLC mỗi 5 giây
            };
            _plcDataReaderTimer.Tick += PlcDataReaderTimer_Tick;
            // _plcDataReaderTimer.Start(); // Sẽ start sau khi PLC kết nối thành công
        }

        private void InitializeShiftChangeTimer()
        {
            _shiftChangeTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(1) // Kiểm tra chuyển ca mỗi phút
            };
            _shiftChangeTimer.Tick += ShiftChangeTimer_Tick;
            _shiftChangeTimer.Start();
        }

        private void UpdatePlcDataReaderTimerState()
        {
            if (_plcDataReaderTimer == null || _plcManager == null)
            {
                // Chưa sẵn sàng để cập nhật trạng thái timer
                return;
            }

            // Kiểm tra xem có PLC nào đang thực sự kết nối không
            // bằng cách duyệt qua thông tin kết nối trong PlcConnectionManager
            bool anyPlcConnected = _plcManager.GetAllPlcConnectionInfos().Any(info => info.IsConnected);

            if (anyPlcConnected)
            {
                if (!_plcDataReaderTimer.IsEnabled) // Nếu có PLC kết nối và timer chưa chạy
                {
                    _plcDataReaderTimer.Start();
                    Debug.WriteLine("PLC Data Reader Timer STARTED because at least one PLC is connected.");
                    _logger?.LogInfo("PLC Data Reader Timer STARTED because at least one PLC is connected.");
                }
            }
            else // Không có PLC nào đang kết nối
            {
                if (_plcDataReaderTimer.IsEnabled) // Nếu không có PLC kết nối và timer đang chạy
                {
                    _plcDataReaderTimer.Stop();
                    Debug.WriteLine("PLC Data Reader Timer STOPPED because no PLCs are connected.");
                    _logger?.LogInfo("PLC Data Reader Timer STOPPED because no PLCs are connected.");
                }
            }
        }

        private async void PlcDataReaderTimer_Tick(object sender, EventArgs e)
        {
            if (_plcManager == null) return;

            // Lấy danh sách tất cả các PLC ID đã được cấu hình và đang có service (đã thử kết nối)
            var allConfiguredPlcIds = _plcManager.GetAllPlcConnectionInfos().Select(info => info.Id).ToList();

            foreach (var plcId in allConfiguredPlcIds)
            {
                var plcService = _plcManager.GetPlcService(plcId);
                var plcConfigInfo = _plcManager.GetPlcConnectionInfo(plcId); // Lấy thêm config info để biết Name

                if (plcService == null || !plcService.IsConnected || plcConfigInfo == null)
                {
                    // Bỏ qua nếu PLC không có service, không kết nối, hoặc không có config info
                    continue;
                }

                Debug.WriteLine($"PlcDataReaderTimer_Tick: Processing PLC ID '{plcId}' ({plcConfigInfo.Name})");

                // VÍ DỤ 1: Nếu PLC này là PLC đọc mã (dựa vào ID hoặc Name từ config)
                if (plcConfigInfo.Id.Equals("PLC1_Reader", StringComparison.OrdinalIgnoreCase) ||
                    plcConfigInfo.Name.Contains("Đọc Mã", StringComparison.OrdinalIgnoreCase))
                {
                    PlcReadResult productCodeReadResult = await plcService.ReadAsync(PlcDeviceAddress.ProductCode_ReaderStation_StartWord);
                    if (!productCodeReadResult.IsSuccess)
                    {
                        AddNewError($"{plcId}_ReadFail_Code", $"{plcConfigInfo.Name}: Read Product Code Error. Details: {productCodeReadResult.Message}");
                    }
                    else
                    {
                        string productCode = PlcDataMapper.MapToApplicationType<string>(productCodeReadResult.Value, plcService.GetRegisterInfo(PlcDeviceAddress.ProductCode_ReaderStation_StartWord));
                        if (!string.IsNullOrWhiteSpace(productCode))
                        {
                            PlcReadResult sequenceForCodeReadResult = await plcService.ReadAsync(PlcDeviceAddress.CurrentProductSequenceNumberWord); // Giả sử địa chỉ này cũng trên PLC đọc mã
                            if (!sequenceForCodeReadResult.IsSuccess)
                            {
                                AddNewError($"{plcId}_ReadFail_SeqForCode", $"{plcConfigInfo.Name}: Read Sequence for Code Error. Details: {sequenceForCodeReadResult.Message}");
                            }
                            else
                            {
                                short seqForCodeShort = PlcDataMapper.MapToApplicationType<short>(sequenceForCodeReadResult.Value, plcService.GetRegisterInfo(PlcDeviceAddress.CurrentProductSequenceNumberWord));
                                if (seqForCodeShort > 0)
                                {
                                    _dataAggregator.ReceiveProductCode(seqForCodeShort, productCode.Trim());
                                }
                            }
                        }
                    }
                }

                if (plcConfigInfo.Id.Equals("PLC1_Reader", StringComparison.OrdinalIgnoreCase) ||
                    plcConfigInfo.Name.Contains("Đọc Mã", StringComparison.OrdinalIgnoreCase))
                {
                    PlcReadResult productCodeReadResult = await plcService.ReadAsync(PlcDeviceAddress.ProductCode_ReaderStation_StartWord);
                    if (!productCodeReadResult.IsSuccess)
                    {
                        AddNewError($"{plcId}_ReadFail_Code", $"{plcConfigInfo.Name}: Read Product Code Error. Details: {productCodeReadResult.Message}");
                    }
                    else
                    {
                        string productCode = PlcDataMapper.MapToApplicationType<string>(productCodeReadResult.Value, plcService.GetRegisterInfo(PlcDeviceAddress.ProductCode_ReaderStation_StartWord));
                        if (!string.IsNullOrWhiteSpace(productCode))
                        {
                            PlcReadResult sequenceForCodeReadResult = await plcService.ReadAsync(PlcDeviceAddress.CurrentProductSequenceNumberWord); // Giả sử địa chỉ này cũng trên PLC đọc mã
                            if (!sequenceForCodeReadResult.IsSuccess)
                            {
                                AddNewError($"{plcId}_ReadFail_SeqForCode", $"{plcConfigInfo.Name}: Read Sequence for Code Error. Details: {sequenceForCodeReadResult.Message}");
                            }
                            else
                            {
                                short seqForCodeShort = PlcDataMapper.MapToApplicationType<short>(sequenceForCodeReadResult.Value, plcService.GetRegisterInfo(PlcDeviceAddress.CurrentProductSequenceNumberWord));
                                if (seqForCodeShort > 0)
                                {
                                    _dataAggregator.ReceiveProductCode(seqForCodeShort, productCode.Trim());
                                }
                            }
                        }
                    }
                }

                //else if (plcConfigInfo.Id.Equals("PLC8_Tester", StringComparison.OrdinalIgnoreCase) ||
                //         plcConfigInfo.Name.Contains("Máy Test", StringComparison.OrdinalIgnoreCase))
                //{
                //    PlcReadResult seqNumReadResult = await plcService.ReadAsync(PlcDeviceAddress.TestStation09_ProductSequenceNumberWord); // Địa chỉ ví dụ
                //    PlcReadResult testResultReadResult = await plcService.ReadAsync(PlcDeviceAddress.TestStation09_ResultWord); // Địa chỉ ví dụ

                //    if (!seqNumReadResult.IsSuccess)
                //    {
                //        AddNewError($"{plcId}_ReadFail_TestSeq", $"{plcConfigInfo.Name}: Read Test Sequence Error. Details: {seqNumReadResult.Message}");
                //    }
                //    if (!testResultReadResult.IsSuccess)
                //    {
                //        AddNewError($"{plcId}_ReadFail_TestResult", $"{plcConfigInfo.Name}: Read Test Result Error. Details: {testResultReadResult.Message}");
                //    }

                //    if (seqNumReadResult.IsSuccess && testResultReadResult.IsSuccess)
                //    {
                //        short seqNumShort = PlcDataMapper.MapToApplicationType<short>(seqNumReadResult.Value, plcService.GetRegisterInfo(PlcDeviceAddress.TestStation09_ProductSequenceNumberWord));
                //        // Giả sử kết quả test là một WORD (short) mà TestResultStatus có thể được ép kiểu từ đó
                //        short testResultShort = PlcDataMapper.MapToApplicationType<short>(testResultReadResult.Value, plcService.GetRegisterInfo(PlcDeviceAddress.TestStation09_ResultWord));
                //        TestResultStatus testResult = (TestResultStatus)testResultShort; // Cẩn thận với ép kiểu trực tiếp này

                //        if (seqNumShort > 0 && (testResult == TestResultStatus.OK || testResult == TestResultStatus.NG))
                //        {
                //            _dataAggregator.ReceiveSequenceAndTestResult(seqNumShort, testResult, $"{plcConfigInfo.Name}_TestStation09"); // Sử dụng tên PLC cho testMachineId
                //        }
                //    }
                //}
            }

            // Đọc dữ liệu cho báo cáo từ PLC_Test_Machine_1
            await ReadProductionDataFromPlc();

            // Cập nhật trạng thái hệ thống cho Layout Controls
            await UpdateSystemStatusDisplay();

            _dataAggregator.CleanupStaleRecords(TimeSpan.FromMinutes(5));
        }

        /// <summary>
        /// Timer để kiểm tra chuyển ca và lưu dữ liệu tự động
        /// </summary>
        private async void ShiftChangeTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                // Kiểm tra chuyển ca
                if (WorkShiftHelper.IsShiftChangeTime())
                {
                    var now = DateTime.Now;
                    // Tránh lưu trùng lặp trong cùng phút
                    if ((now - _lastShiftChangeCheck).TotalMinutes >= 1)
                    {
                        _lastShiftChangeCheck = now;
                        await SaveProductionDataOnShiftChange();
                        Debug.WriteLine($"Shift change detected at {now:HH:mm:ss}, production data saved.");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ShiftChangeTimer_Tick: {ex.Message}");
            }
        }

        /// <summary>
        /// Đọc dữ liệu sản xuất từ PLC_Test_Machine_1 cho báo cáo
        /// </summary>
        private async Task ReadProductionDataFromPlc()
        {
            try
            {
                if (!_plcManager.IsPlcConnected("PLC_Test_Machine_1"))
                    return;

                var productionData = await _productionDataService.ReadProductionDataFromPlcAsync("ALL");

                // Kiểm tra có lỗi mới không
                if (!string.IsNullOrEmpty(productionData.Error_Code))
                {
                    await SaveProductionDataWithType(productionData, "ErrorHistory");
                    Debug.WriteLine($"New error detected: {productionData.Error_Code}, data saved.");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error reading production data from PLC: {ex.Message}");
            }
        }

        /// <summary>
        /// Cập nhật hiển thị trạng thái hệ thống trên Layout Controls
        /// </summary>
        private async Task UpdateSystemStatusDisplay()
        {
            try
            {
                // Mặc định là không kết nối
                bool systemRunning = false;
                int totalOK = 0;
                int totalNG = 0;
                bool hasConnection = false;

                // Kiểm tra kết nối PLC và đọc dữ liệu trạng thái - CHỈ TỪ PLC_Real_Test
                if (_plcManager != null && _plcManager.IsPlcConnected("PLC_Test_Machine_1"))
                {
                    var plcService = _plcManager.GetPlcService("PLC_Test_Machine_1");
                    if (plcService != null)
                    {
                        hasConnection = true;

                        // Đọc trạng thái chạy/dừng hệ thống
                        var runningResult = await plcService.ReadAsync(PlcDeviceAddress.SystemRunningStatus);
                        if (runningResult.IsSuccess)
                        {
                            systemRunning = PlcDataMapper.MapToApplicationType<bool>(runningResult.Value,
                                plcService.GetRegisterInfo(PlcDeviceAddress.SystemRunningStatus));
                        }

                        // Đọc tổng sản phẩm OK
                        var okResult = await plcService.ReadAsync(PlcDeviceAddress.SystemTotalOK);
                        if (okResult.IsSuccess)
                        {
                            totalOK = PlcDataMapper.MapToApplicationType<int>(okResult.Value,
                                plcService.GetRegisterInfo(PlcDeviceAddress.SystemTotalOK));
                        }

                        // Đọc tổng sản phẩm NG
                        var ngResult = await plcService.ReadAsync(PlcDeviceAddress.SystemTotalNG);
                        if (ngResult.IsSuccess)
                        {
                            totalNG = PlcDataMapper.MapToApplicationType<int>(ngResult.Value,
                                plcService.GetRegisterInfo(PlcDeviceAddress.SystemTotalNG));
                        }

                        // Đọc mã lỗi Mainline
                        var mainlineFaultResult = await plcService.ReadAsync(PlcDeviceAddress.MainlineFaultCode);
                        if (mainlineFaultResult.IsSuccess)
                        {
                            _faultService.ProcessFaultData(mainlineFaultResult.Value, "mainline");
                        }

                        // Đọc mã lỗi Inspection
                        var inspectionFaultResult = await plcService.ReadAsync(PlcDeviceAddress.InspectionFaultCode);
                        if (inspectionFaultResult.IsSuccess)
                        {
                            _faultService.ProcessFaultData(inspectionFaultResult.Value, "inspection");
                        }
                    }
                }

                // Cập nhật UI trên UI thread
                Dispatcher.Invoke(() =>
                {
                    if (!hasConnection)
                    {
                        SystemStatusRun.Text = "Không kết nối";
                        SystemStatusRun.Foreground = new SolidColorBrush(Color.FromRgb(0xFF, 0x6B, 0x6B)); // Red
                        SystemTotalOKRun.Text = "--";
                        SystemTotalNGRun.Text = "--";
                        MainlineFaultRun.Text = "Không kết nối";
                        MainlineFaultRun.Foreground = new SolidColorBrush(Color.FromRgb(0xFF, 0x6B, 0x6B));
                        InspectionFaultRun.Text = "Không kết nối";
                        InspectionFaultRun.Foreground = new SolidColorBrush(Color.FromRgb(0xFF, 0x6B, 0x6B));
                    }
                    else
                    {
                        SystemStatusRun.Text = systemRunning ? "Đang chạy" : "Dừng";
                        SystemStatusRun.Foreground = systemRunning ?
                            new SolidColorBrush(Color.FromRgb(0x4E, 0xCD, 0xC4)) : // Green
                            new SolidColorBrush(Color.FromRgb(0xFF, 0x6B, 0x6B));   // Red

                        SystemTotalOKRun.Text = totalOK.ToString();
                        SystemTotalNGRun.Text = totalNG.ToString();

                        // Cập nhật hiển thị lỗi
                        UpdateFaultDisplay();
                    }
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating system status display: {ex.Message}");

                // Hiển thị lỗi trên UI
                Dispatcher.Invoke(() =>
                {
                    SystemStatusRun.Text = "Lỗi đọc";
                    SystemStatusRun.Foreground = new SolidColorBrush(Color.FromRgb(0xFF, 0x6B, 0x6B));
                    SystemTotalOKRun.Text = "--";
                    SystemTotalNGRun.Text = "--";
                    MainlineFaultRun.Text = "Lỗi đọc";
                    MainlineFaultRun.Foreground = new SolidColorBrush(Color.FromRgb(0xFF, 0x6B, 0x6B));
                    InspectionFaultRun.Text = "Lỗi đọc";
                    InspectionFaultRun.Foreground = new SolidColorBrush(Color.FromRgb(0xFF, 0x6B, 0x6B));
                });
            }
        }

        /// <summary>
        /// Cập nhật hiển thị lỗi từ PlcFaultService
        /// </summary>
        private void UpdateFaultDisplay()
        {
            try
            {
                // Cập nhật hiển thị header (giữ lại cho thông tin tổng quan)
                MainlineFaultRun.Text = _faultService.MainlineFaultDisplay;
                MainlineFaultRun.Foreground = new SolidColorBrush((Color)System.Windows.Media.ColorConverter.ConvertFromString(_faultService.MainlineFaultColor));

                InspectionFaultRun.Text = _faultService.InspectionFaultDisplay;
                InspectionFaultRun.Foreground = new SolidColorBrush((Color)System.Windows.Media.ColorConverter.ConvertFromString(_faultService.InspectionFaultColor));

                // Hiển thị lỗi trên canvas layout
                if (_canvasFaultDisplayService != null)
                {
                    // Hiển thị lỗi Mainline trên canvas
                    if (_faultService.CurrentMainlineFault.IsActive)
                    {
                        _canvasFaultDisplayService.DisplayFault(_faultService.CurrentMainlineFault, "mainline");

                        // Toast notification cho lỗi Mainline
                        ShowFaultToast(_faultService.CurrentMainlineFault, "Mainline");
                    }
                    else
                    {
                        _canvasFaultDisplayService.ClearFault(_faultService.CurrentMainlineFault.FaultCode, "mainline");
                    }

                    // Hiển thị lỗi Inspection trên canvas
                    if (_faultService.CurrentInspectionFault.IsActive)
                    {
                        _canvasFaultDisplayService.DisplayFault(_faultService.CurrentInspectionFault, "inspection");

                        // Toast notification cho lỗi Inspection
                        ShowFaultToast(_faultService.CurrentInspectionFault, "Inspection");
                    }
                    else
                    {
                        _canvasFaultDisplayService.ClearFault(_faultService.CurrentInspectionFault.FaultCode, "inspection");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating fault display: {ex.Message}");
                // Fallback to default display
                MainlineFaultRun.Text = "Lỗi hiển thị";
                MainlineFaultRun.Foreground = new SolidColorBrush(Color.FromRgb(0xFF, 0x6B, 0x6B));
                InspectionFaultRun.Text = "Lỗi hiển thị";
                InspectionFaultRun.Foreground = new SolidColorBrush(Color.FromRgb(0xFF, 0x6B, 0x6B));
            }
        }

        /// <summary>
        /// Hiển thị toast notification cho lỗi
        /// </summary>
        private void ShowFaultToast(PlcFaultInfo faultInfo, string systemName)
        {
            if (faultInfo == null || !faultInfo.IsActive) return;

            string toastMessage = $"🚨 LỖI {systemName.ToUpper()}\n" +
                                 $"Mã lỗi: {faultInfo.FaultCode}\n" +
                                 $"{faultInfo.StationsText}\n" +
                                 $"{faultInfo.Message}";

            _toastNotification.Position = ToastPosition.BottomRight;
            _toastNotification.ShowError(toastMessage);

            // Thêm vào danh sách lỗi
            AddNewError($"{systemName}_{faultInfo.FaultCode}", toastMessage);
        }

        /// <summary>
        /// Lưu dữ liệu sản xuất khi chuyển ca
        /// </summary>
        private async Task SaveProductionDataOnShiftChange()
        {
            try
            {
                var productionData = await _productionDataService.ReadProductionDataFromPlcAsync("ALL");
                productionData.ReportType = "Production";
                productionData.Notes = "Tự động lưu khi chuyển ca";

                await _productionDataService.SaveProductionDataAsync(productionData);
                Debug.WriteLine("Production data saved on shift change.");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving production data on shift change: {ex.Message}");
            }
        }

        /// <summary>
        /// Lưu dữ liệu sản xuất với loại báo cáo cụ thể
        /// </summary>
        private async Task SaveProductionDataWithType(ProductionData data, string reportType)
        {
            try
            {
                data.ReportType = reportType;
                await _productionDataService.SaveProductionDataAsync(data);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving production data with type {reportType}: {ex.Message}");
            }
        }

        /// <summary>
        /// Lưu dữ liệu thao tác chậm (được gọi từ nút Thao tác chậm)
        /// </summary>
        public async Task SaveSlowOperationData()
        {
            try
            {
                // Đọc dữ liệu từ 18 trạm
                for (int i = 1; i <= 18; i++)
                {
                    var stationData = await _productionDataService.ReadProductionDataFromPlcAsync($"ST{i}");
                    stationData.ReportType = "SlowOperation";
                    stationData.Notes = $"Thao tác chậm tại trạm ST{i}";

                    await _productionDataService.SaveProductionDataAsync(stationData);
                }

                Debug.WriteLine("Slow operation data saved for all 18 stations.");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving slow operation data: {ex.Message}");
            }
        }

        /// <summary>
        /// Lưu dữ liệu đo thao tác (được gọi từ nút Đo thao tác)
        /// </summary>
        public async Task SaveMeasureOperationData()
        {
            try
            {
                // Đo 10 lần liên tiếp cho 18 vị trí
                for (int station = 1; station <= 18; station++)
                {
                    for (int measurement = 1; measurement <= 10; measurement++)
                    {
                        var measureData = await _productionDataService.ReadProductionDataFromPlcAsync($"ST{station}");
                        measureData.ReportType = "MeasureOperation";
                        measureData.Notes = $"Đo lần {measurement}/10 tại trạm ST{station}";

                        await _productionDataService.SaveProductionDataAsync(measureData);

                        // Delay nhỏ giữa các lần đo
                        await Task.Delay(100);
                    }
                }

                Debug.WriteLine("Measure operation data saved: 10 measurements for 18 stations.");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving measure operation data: {ex.Message}");
            }
        }
        private async void MainWindow_Closing(object sender, CancelEventArgs e)
        {
            // Ngắt kết nối tất cả PLC khi đóng ứng dụng
            Debug.WriteLine("MainWindow closing. Disconnecting all PLCs...");
            if (_plcManager != null)
            {
                await _plcManager.DisconnectAllAsync();
            }
            Debug.WriteLine("All PLCs disconnect command sent.");
        }

        private void InitializeToastNotification()
        {
            _toastNotification = ToastNotificationPanel.Instance;
            _toastNotification.SetContainer(ToastNotificationContainer);
            _toastNotification.Position = ToastPosition.BottomRight;
            _toastNotification.Duration = 3000;
            _toastNotification.AnimationDuration = 300;
            _toastNotification.CornerRadius = 10;
            _toastNotification.EnableShadow = true;
        }

        private void InitializeDashboardCharts()
        {
            try
            {
                // Dashboard charts are now handled by individual UserControls
                // Each control manages its own ViewModel and data binding
                System.Diagnostics.Debug.WriteLine("Dashboard charts initialized successfully (via UserControls)");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing dashboard charts: {ex.Message}");
            }
        }

        public void ShowToast(string title, string message, ToastType type)
        {
            try
            {
                string fullMessage = string.IsNullOrEmpty(title) ? message : $"{title}: {message}";
                _toastNotification.Show(fullMessage, type);
                System.Diagnostics.Debug.WriteLine($"Toast shown: {type} - {fullMessage}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing toast: {ex.Message}");
            }
        }

        // 3 hàm bên dưới phục vụ mô phỏng lỗi 

        //private void InitializeErrorSimulation()
        //{
        //    _plcErrorSimulatorTimer = new DispatcherTimer
        //    {
        //        Interval = TimeSpan.FromSeconds(_random.Next(5, 15)) // Random interval for new errors
        //    };
        //    _plcErrorSimulatorTimer.Tick += PlcErrorSimulatorTimer_Tick;
        //    _plcErrorSimulatorTimer.Start();
        //}

        //private void PlcErrorSimulatorTimer_Tick(object sender, EventArgs e)
        //{
        //    _simulatedErrorCounter++;
        //    string errorCode = $"PLC_ERR_{_simulatedErrorCounter:D3}";
        //    string message = GetRandomErrorMessage();
        //    AddNewError(errorCode, message);

        //    // Reschedule with a new random interval
        //    _plcErrorSimulatorTimer.Interval = TimeSpan.FromSeconds(_random.Next(8, 20));
        //}

        //private string GetRandomErrorMessage()
        //{
        //    string[] messages = {
        //        "Sensor ST05_ProductPresence failed.", "Emergency Stop activated on Line 1.",
        //        "Conveyor motor overload at Mainline.Station03.", "Test Station 09: Communication Timeout.",
        //        "Lifter mechanism jammed.", "Safety door SFT02 open.", "PLC Battery Low."
        //    };
        //    return messages[_random.Next(messages.Length)];
        //}

        public void AddNewError(string errorId, string message)
        {
            // Ensure execution on UI thread if called from elsewhere
            if (!Dispatcher.CheckAccess())
            {
                Dispatcher.Invoke(() => AddNewError(errorId, message));
                return;
            }

            var newError = new ErrorNotification(errorId, message);
            AllErrors.Insert(0, newError); // Add to top of the list
            NewErrorCount++;

            _toastNotification.Position = ToastPosition.BottomRight;
            _toastNotification.ShowError(message);
            System.Diagnostics.Debug.WriteLine($"New Error: {errorId} - {message}");
        }

        private void ErrorBellButton_Click(object sender, RoutedEventArgs e)
        {
            IsErrorListPopupOpen = !IsErrorListPopupOpen;
            if (IsErrorListPopupOpen)
            {
                // Mark viewed errors as not new, but only if they were new
                foreach (var err in AllErrors.Where(er => er.IsNew).ToList()) // ToList to avoid modification issues
                {
                    err.IsNew = false;
                }
                NewErrorCount = 0; // Reset badge count
                // The BellShakeStoryboard will stop via DataTrigger ExitActions
            }
        }
        private void ClearAllErrorsButton_Click(object sender, RoutedEventArgs e)
        {
            // This only clears the display. PLC errors would need PLC acknowledgement.
            AllErrors.Clear();
            NewErrorCount = 0;
            IsErrorListPopupOpen = false; // Optionally close popup
        }

        // --- INotifyPropertyChanged Implementation ---
        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void ViewportBorder_MouseWheel(object sender, MouseWheelEventArgs e)
        {
            Point mousePosition = e.GetPosition(ZoomPanCanvas); // Zoom relative to mouse position within the canvas
            double currentScale = ViewScaleTransform.ScaleX;
            double newScale;

            if (e.Delta > 0) // Zoom in
            {
                newScale = currentScale * ZoomSpeed;
            }
            else // Zoom out
            {
                newScale = currentScale / ZoomSpeed;
            }

            newScale = Math.Max(MinZoom, Math.Min(newScale, MaxZoom)); // Clamp zoom level

            if (Math.Abs(newScale - currentScale) > 0.001) // Check if scale actually changed
            {
                // Calculate the translation adjustment needed to keep the mouse point fixed
                double oldX = ViewTranslateTransform.X;
                double oldY = ViewTranslateTransform.Y;

                ViewScaleTransform.ScaleX = newScale;
                ViewScaleTransform.ScaleY = newScale;

                // Adjust translation so the point under the mouse remains the same
                ViewTranslateTransform.X = mousePosition.X - (mousePosition.X - oldX) * (newScale / currentScale);
                ViewTranslateTransform.Y = mousePosition.Y - (mousePosition.Y - oldY) * (newScale / currentScale);
            }
        }

        private void ViewportBorder_MouseDown(object sender, MouseButtonEventArgs e)
        {
            // Allow panning with Middle or Right mouse button
            if (e.MiddleButton == MouseButtonState.Pressed || e.RightButton == MouseButtonState.Pressed)
            {
                _isPanning = true;
                _panStartPoint = e.GetPosition(ViewportBorder); // Pan relative to ViewportBorder
                _panStartTranslate = new Point(ViewTranslateTransform.X, ViewTranslateTransform.Y);
                ViewportBorder.Cursor = Cursors.ScrollAll;
                ViewportBorder.CaptureMouse(); // Capture mouse to ensure MouseMove/Up events are received
            }
        }

        private void ViewportBorder_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isPanning)
            {
                Point currentMousePosition = e.GetPosition(ViewportBorder);
                Vector delta = currentMousePosition - _panStartPoint;
                ViewTranslateTransform.X = _panStartTranslate.X + delta.X;
                ViewTranslateTransform.Y = _panStartTranslate.Y + delta.Y;
            }
        }

        private void ViewportBorder_MouseUp(object sender, MouseButtonEventArgs e)
        {
            if (_isPanning)
            {
                _isPanning = false;
                ViewportBorder.Cursor = Cursors.Arrow;
                ViewportBorder.ReleaseMouseCapture();
            }
        }

        private void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            ResetViewTransforms();
        }

        private void ResetViewTransforms()
        {
            ViewScaleTransform.ScaleX = 1.0;
            ViewScaleTransform.ScaleY = 1.0;
            ViewTranslateTransform.X = 0.0;
            ViewTranslateTransform.Y = 0.0;
        }



        private void LoadLayout(string layoutName)
        {
            // 1. Clean up previous layout and event subscriptions
            if (_currentLoadedLayout != null)
            {
                if (_currentLoadedLayout == _mainlineLayoutInstance && _mainlineLayoutInstance != null)
                {
                    _mainlineLayoutInstance.ProductExitingLine -= Mainline_ProductExitingLine;
                    System.Diagnostics.Debug.WriteLine("Unsubscribed from MainlineLayout.ProductExitingLine");
                }
                // If _currentLoadedLayout was _inspectionLayoutInstance, no specific events to unsub from in this example
            }

            ZoomPanCanvas.Children.Clear();
            _currentLoadedLayout = null; // Reset general reference
            _mainlineLayoutInstance = null; // Nullify specific instances, they will be re-created if chosen
            _inspectionLayoutInstance = null;

            UserControl layoutToLoad = null;

            // 2. Create and configure new layout
            switch (layoutName)
            {
                case "Mainline":
                    _mainlineLayoutInstance = new MainlineLayout();
                    _mainlineLayoutInstance.ProductExitingLine += Mainline_ProductExitingLine;
                    System.Diagnostics.Debug.WriteLine("Subscribed to MainlineLayout.ProductExitingLine");
                    layoutToLoad = _mainlineLayoutInstance;
                    break;
                case "Inspection":
                    _inspectionLayoutInstance = new InspectionLayout();
                    layoutToLoad = _inspectionLayoutInstance;
                    break;
                default:
                    TextBlock errorText = new TextBlock
                    {
                        Text = $"Layout '{layoutName}' not found.",
                        Foreground = Brushes.Red,
                        FontSize = 16,
                        HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center
                    };
                    // For centering, it's better if ZoomPanCanvas had a child Grid that does the centering
                    // or set Canvas.Left/Top after measuring, but this is simpler for an error message:
                    ZoomPanCanvas.Children.Add(errorText);
                    // A simple way to somewhat center without complex measurement:
                    errorText.Loaded += (s, e) => {
                        Canvas.SetLeft(errorText, (ZoomPanCanvas.ActualWidth - errorText.ActualWidth) / 2);
                        Canvas.SetTop(errorText, (ZoomPanCanvas.ActualHeight - errorText.ActualHeight) / 2);
                    };
                    break;
            }

            if (layoutToLoad != null)
            {
                _currentLoadedLayout = layoutToLoad; // Store reference to the loaded layout
                ZoomPanCanvas.Children.Add(_currentLoadedLayout);
                ResetViewTransforms(); // Reset view for the new layout
                System.Diagnostics.Debug.WriteLine($"Loaded layout: {layoutName}");

                // Update dashboard information based on layout
                UpdateDashboardForLayout(layoutName);

                // Update canvas references for fault display service
                UpdateCanvasFaultDisplayReferences(layoutName);
            }
        }

        private void UpdateDashboardForLayout(string layoutName)
        {
            switch (layoutName)
            {
                case "Mainline":
                    // Show normal production dashboard
                    ShowMainlineDashboard();
                    break;
                case "Inspection":
                    // Show inspection/maintenance dashboard
                    ShowInspectionDashboard();
                    break;
            }
        }

        /// <summary>
        /// Cập nhật canvas references cho fault display service
        /// </summary>
        private void UpdateCanvasFaultDisplayReferences(string layoutName)
        {
            if (_canvasFaultDisplayService == null) return;

            Canvas? mainlineCanvas = null;
            Canvas? inspectionCanvas = null;

            // Tìm ProductLayerCanvas trong layout hiện tại
            if (_mainlineLayoutInstance != null && layoutName == "Mainline")
            {
                mainlineCanvas = FindCanvasInLayout(_mainlineLayoutInstance, "ProductLayerCanvas");
            }
            else if (_inspectionLayoutInstance != null && layoutName == "Inspection")
            {
                inspectionCanvas = FindCanvasInLayout(_inspectionLayoutInstance, "ProductLayerCanvas");
            }

            // Cập nhật references
            _canvasFaultDisplayService.SetCanvasReferences(mainlineCanvas, inspectionCanvas);

            System.Diagnostics.Debug.WriteLine($"Updated canvas references for {layoutName} - " +
                $"Mainline: {(mainlineCanvas != null ? "Found" : "Not found")}, " +
                $"Inspection: {(inspectionCanvas != null ? "Found" : "Not found")}");
        }

        /// <summary>
        /// Tìm Canvas với tên cụ thể trong UserControl
        /// </summary>
        private Canvas? FindCanvasInLayout(UserControl layout, string canvasName)
        {
            return FindChildByName<Canvas>(layout, canvasName);
        }

        /// <summary>
        /// Tìm child control theo tên
        /// </summary>
        private T? FindChildByName<T>(DependencyObject parent, string name) where T : FrameworkElement
        {
            if (parent == null) return null;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is T element && element.Name == name)
                {
                    return element;
                }

                var result = FindChildByName<T>(child, name);
                if (result != null)
                {
                    return result;
                }
            }

            return null;
        }

        private void ShowMainlineDashboard()
        {
            // Enable normal dashboard functionality
            if (DailyPlanDataGrid != null)
            {
                DailyPlanDataGrid.Visibility = Visibility.Visible;
            }

            // Restore normal dashboard elements if they were hidden
            RestoreNormalDashboard();

            System.Diagnostics.Debug.WriteLine("Dashboard configured for Mainline layout");
        }

        private void ShowInspectionDashboard()
        {
            // Show maintenance/inspection information
            if (DailyPlanDataGrid != null)
            {
                // Hide the normal plan grid and show maintenance info
                DailyPlanDataGrid.Visibility = Visibility.Collapsed;

                // Create a maintenance status display
                CreateMaintenanceStatusDisplay();
            }

            System.Diagnostics.Debug.WriteLine("Dashboard configured for Inspection layout - showing maintenance info");
        }

        private void RestoreNormalDashboard()
        {
            // Find the parent container and restore original DataGrid if needed
            var parent = DailyPlanDataGrid?.Parent as Border;
            if (parent != null && !(parent.Child is DataGrid))
            {
                // Restore the original DataGrid
                parent.Child = DailyPlanDataGrid;
            }
        }

        private void CreateMaintenanceStatusDisplay()
        {
            // Find the parent container of the DataGrid
            var parent = DailyPlanDataGrid?.Parent as Border;
            if (parent != null)
            {
                // Create maintenance status panel
                var maintenancePanel = new StackPanel
                {
                    Orientation = System.Windows.Controls.Orientation.Vertical,
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(20)
                };

                // Add maintenance status text
                var statusText = new TextBlock
                {
                    Text = "MAINTAINING",
                    FontSize = 24,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Color.FromRgb(231, 76, 60)), // Red color
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 10)
                };

                var descriptionText = new TextBlock
                {
                    Text = "Inspection line is currently under maintenance.\nPlease check back later for production status.",
                    FontSize = 16,
                    Foreground = new SolidColorBrush(Color.FromRgb(52, 73, 94)), // Dark blue-gray
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                    TextAlignment = TextAlignment.Center,
                    TextWrapping = TextWrapping.Wrap
                };

                maintenancePanel.Children.Add(statusText);
                maintenancePanel.Children.Add(descriptionText);

                // Replace DataGrid content with maintenance panel
                parent.Child = maintenancePanel;
            }
        }

        private void Mainline_ProductExitingLine(object sender, ProductItemEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"Product {e.Product.ProductId} received by MainWindow from Mainline.");

            // Check if the InspectionLayout is currently loaded AND is the _inspectionLayoutInstance we expect
            if (_currentLoadedLayout == _inspectionLayoutInstance && _inspectionLayoutInstance != null)
            {
                _inspectionLayoutInstance.AcceptProduct(e.Product);
                System.Diagnostics.Debug.WriteLine($"Product {e.Product.ProductId} passed to active InspectionLayout.");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"Product {e.Product.ProductId} exited Mainline. InspectionLayout is not the current view or is invalid. Product discarded from UI flow.");
                // Product is effectively lost from the UI simulation at this point if not handled
            }
        }

        public class RelayCommand<T> : ICommand
        {
            private readonly Action<T> _execute;
            private readonly Func<T, bool> _canExecute;

            public event EventHandler CanExecuteChanged
            {
                add { CommandManager.RequerySuggested += value; }
                remove { CommandManager.RequerySuggested -= value; }
            }

            public RelayCommand(Action<T> execute, Func<T, bool> canExecute = null)
            {
                _execute = execute ?? throw new ArgumentNullException(nameof(execute));
                _canExecute = canExecute;
            }

            public bool CanExecute(object parameter)
            {
                return _canExecute == null || _canExecute((T)parameter);
            }

            public void Execute(object parameter)
            {
                _execute((T)parameter);
            }
        }

        private void UpdateUserInfo()
        {
            if (UserSession.IsLoggedIn)
            {
                UserFullNameTextBlock.Text = UserSession.CurrentUser!.Fullname;
                // Hiển thị ca làm việc với xuống dòng: "Ca hành chính\n8 giờ - 17 giờ"
                var shift = UserSession.CurrentShift!;
                UserShiftTextBlock.Text = $"{shift.Name}\n{shift.TimeRange}";

                // Update menu visibility based on user role
                UpdateMenuVisibility();
            }
            else
            {
                UserFullNameTextBlock.Text = "Chưa đăng nhập";
                UserShiftTextBlock.Text = "";
            }
        }

        private void UpdateMenuVisibility()
        {
            // Only Admin can see Users management
            var isAdmin = UserSession.CurrentUser?.Role == "Administrator";

            // Find the Users menu item and hide/show based on permission
            foreach (ListBoxItem item in SidebarMenuListBox.Items)
            {
                if (item.Content?.ToString()?.Contains("Người dùng") == true)
                {
                    item.Visibility = isAdmin ? Visibility.Visible : Visibility.Collapsed;
                    break;
                }
            }
        }

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                if (_dashboardDataService == null) return;

                // Load idle time data
                var idleTimeData = await _dashboardDataService.GetIdleTimeDataAsync();

                Dispatcher.Invoke(() =>
                {
                    TodayTotalIdleTime = FormatTimeSpan(idleTimeData.DailyUsedIdleTime);
                    TodayRemainingIdleTime = FormatTimeSpan(idleTimeData.DailyRemainingIdleTime);
                    MonthTotalIdleTime = FormatTimeSpan(idleTimeData.MonthlyUsedIdleTime);
                    MonthRemainingIdleTime = FormatTimeSpan(idleTimeData.MonthlyRemainingIdleTime);

                    // Update charts data
                    UpdateIdleTimeCharts(idleTimeData);
                });

                // Load production data for today
                var todayProduction = await _dashboardDataService.GetDailyProductionSummaryAsync(DateTime.Today);

                // Load production data for current shift
                var currentShift = UserSession.CurrentShift?.Name ?? "Ca hành chính";
                var shiftProduction = await _dashboardDataService.GetShiftProductionSummaryAsync(DateTime.Today, currentShift);

                Dispatcher.Invoke(() =>
                {
                    UpdateProductionCharts(todayProduction, shiftProduction);
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading dashboard data: {ex.Message}");
            }
        }

        private string FormatTimeSpan(TimeSpan timeSpan)
        {
            if (timeSpan.TotalHours >= 1)
            {
                return $"{(int)timeSpan.TotalHours}:{timeSpan.Minutes:D2}";
            }
            else
            {
                return $"0:{timeSpan.Minutes:D2}";
            }
        }

        private void UpdateIdleTimeCharts(IdleTimeData idleTimeData)
        {
            try
            {
                // Update daily idle time chart (PieChart)
                var dailyUsedMinutes = idleTimeData.DailyUsedIdleTime.TotalMinutes;
                var dailyRemainingMinutes = idleTimeData.DailyRemainingIdleTime.TotalMinutes;

                if (PieChart != null)
                {
                    PieChart.Series = new ObservableCollection<ISeries>
                    {
                        new PieSeries<double>
                        {
                            Values = new[] { dailyUsedMinutes },
                            Name = "Đã dùng",
                            InnerRadius = 35,
                            MaxRadialColumnWidth = 50,
                            Fill = new SolidColorPaint(SKColors.Red)
                        },
                        new PieSeries<double>
                        {
                            Values = new[] { Math.Max(0, dailyRemainingMinutes) },
                            Name = "Còn lại",
                            InnerRadius = 35,
                            MaxRadialColumnWidth = 50,
                            Fill = new SolidColorPaint(SKColors.Green)
                        }
                    };
                }

                // Update monthly idle time chart (PieChart2)
                var monthlyUsedMinutes = idleTimeData.MonthlyUsedIdleTime.TotalMinutes;
                var monthlyRemainingMinutes = idleTimeData.MonthlyRemainingIdleTime.TotalMinutes;

                if (PieChart2 != null)
                {
                    PieChart2.Series = new ObservableCollection<ISeries>
                    {
                        new PieSeries<double>
                        {
                            Values = new[] { monthlyUsedMinutes },
                            Name = "Đã dùng",
                            InnerRadius = 35,
                            MaxRadialColumnWidth = 50,
                            Fill = new SolidColorPaint(SKColors.Red)
                        },
                        new PieSeries<double>
                        {
                            Values = new[] { Math.Max(0, monthlyRemainingMinutes) },
                            Name = "Còn lại",
                            InnerRadius = 35,
                            MaxRadialColumnWidth = 50,
                            Fill = new SolidColorPaint(SKColors.Green)
                        }
                    };
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating idle time charts: {ex.Message}");
            }
        }

        private void UpdateProductionCharts(ProductionSummaryData todayData, ProductionSummaryData shiftData)
        {
            try
            {
                // Update daily production charts (PieChart3 - left half chart, PieChart4 - right pie chart)
                UpdateDailyProductionCharts(todayData);

                // Update shift production charts (div4 charts)
                UpdateShiftProductionCharts(shiftData);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating production charts: {ex.Message}");
            }
        }

        private void UpdateDailyProductionCharts(ProductionSummaryData data)
        {
            try
            {
                //Useless
                // PieChart3 - Left half chart showing Plan vs Actual vs Gap
                //if (PieChart3 != null)
                //{
                //    PieChart3.Series = new ObservableCollection<ISeries>
                //    {
                //        new PieSeries<double>
                //        {
                //            Values = new[] { (double)data.PlanQuantity },
                //            Name = "Plan",
                //            InnerRadius = 75,
                //            MaxRadialColumnWidth = 50,
                //            Fill = new SolidColorPaint(SKColors.Blue)
                //        },
                //        new PieSeries<double>
                //        {
                //            Values = new[] { (double)data.ActualQuantity },
                //            Name = "Actual",
                //            InnerRadius = 75,
                //            MaxRadialColumnWidth = 50,
                //            Fill = new SolidColorPaint(SKColors.Green)
                //        }
                //    };
                //}

                // PieChart4 - Right pie chart showing OK vs NG vs Rework
                //if (PieChart4 != null)
                //{
                //    PieChart4.Series = new ObservableCollection<ISeries>
                //    {
                //        new PieSeries<double>
                //        {
                //            Values = new[] { (double)data.OkQuantity },
                //            Name = "OK",
                //            Fill = new SolidColorPaint(SKColors.Green)
                //        },
                //        new PieSeries<double>
                //        {
                //            Values = new[] { (double)data.NgQuantity },
                //            Name = "NG",
                //            Fill = new SolidColorPaint(SKColors.Red)
                //        },
                //        new PieSeries<double>
                //        {
                //            Values = new[] { (double)data.ReworkQuantity },
                //            Name = "Rework",
                //            Fill = new SolidColorPaint(SKColors.Orange)
                //        }
                //    };
                //}
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating daily production charts: {ex.Message}");
            }
        }

        private void UpdateShiftProductionCharts(ProductionSummaryData data)
        {
            //Useless
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "Bạn có chắc chắn muốn đăng xuất?",
                "Xác nhận đăng xuất",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                UserSession.Logout();

                // Đóng MainWindow và mở LoginWindow
                LoginWindow loginWindow = new LoginWindow();
                loginWindow.Show();
                this.Close();
            }
        }


    }
}