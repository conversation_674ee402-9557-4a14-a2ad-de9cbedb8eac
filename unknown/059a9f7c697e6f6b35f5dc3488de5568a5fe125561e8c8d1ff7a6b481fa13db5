﻿using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace ZoomableApp.Views
{
    /// <summary>
    /// Interaction logic for ShiftQualityChartControl.xaml
    /// </summary>
    public partial class ShiftQualityChartControl : UserControl
    {
        public ShiftQualityChartControl()
        {
            InitializeComponent();
            // Set DataContext to the ViewModel
            ShiftQualityChart.LegendTextPaint = new SolidColorPaint(SKColors.White);
            this.DataContext = new ViewModels.ShiftQualityChartViewModel();
        }
    }
}
