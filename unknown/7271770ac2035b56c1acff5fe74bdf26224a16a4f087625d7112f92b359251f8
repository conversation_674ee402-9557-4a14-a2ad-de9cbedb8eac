﻿<UserControl x:Class="ZoomableApp.SharedControls.ProductVisualControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:ZoomableApp.SharedControls"
             xmlns:models="clr-namespace:ZoomableApp.Models"
             mc:Ignorable="d"
             d:DesignHeight="109" d:DesignWidth="90"
             Width="90" Height="109">
    <UserControl.Resources>
        <SolidColorBrush x:Key="ProductOKBrush" Color="LightGreen"/>
        <SolidColorBrush x:Key="ProductNGBRUSH" Color="Salmon"/>
        <SolidColorBrush x:Key="ProductTestingBrush" Color="LightYellow"/>
        <SolidColorBrush x:Key="ProductAwaitingBrush" Color="LightBlue"/>
        <SolidColorBrush x:Key="ProductNoneBrush" Color="LightGray"/>
    </UserControl.Resources>

    <Grid>
        <Border
                Background="WhiteSmoke"
                CornerRadius="4"
                BorderBrush="Black"
                BorderThickness="1"
                Margin="70,5,12,96"
                Panel.ZIndex="1"/>
        <Border
                Background="WhiteSmoke"
                CornerRadius="4"
                BorderBrush="Black"
                BorderThickness="1"
                Margin="57,5,25,96"
                Panel.ZIndex="1"/>
        <Border
                Background="WhiteSmoke"
                CornerRadius="4"
                BorderBrush="Black"
                BorderThickness="1"
                Margin="10,5,65,96"
                Panel.ZIndex="1"/>
        <Border
                Background="WhiteSmoke"
                CornerRadius="10,10,0,0"
                BorderBrush="Black"
                BorderThickness="1"
                Margin="0,0,0,90"/>
        <Border
                Background="WhiteSmoke"
                CornerRadius="0,0,10,10"
                BorderBrush="Black"
                BorderThickness="1"
                Margin="0,18,0,0"/>
        <Ellipse Stroke="Black" StrokeThickness="2" Fill="WhiteSmoke" Margin="10,28,10,10"/>

        <!-- Inner circular drum that changes color by status -->
        <Ellipse Canvas.Left="20" Canvas.Top="20" Stroke="Gray" StrokeThickness="1" Margin="15,33,15,15">
            <Ellipse.Style>
                <Style TargetType="Ellipse">
                    <Setter Property="Fill" Value="LightGray"/>
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding CurrentTestResult}" Value="{x:Static models:TestResultStatus.OK}">
                            <Setter Property="Fill" Value="{StaticResource ProductOKBrush}"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding CurrentTestResult}" Value="{x:Static models:TestResultStatus.NG}">
                            <Setter Property="Fill" Value="{StaticResource ProductNGBRUSH}"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding CurrentTestResult}" Value="{x:Static models:TestResultStatus.Testing}">
                            <Setter Property="Fill" Value="{StaticResource ProductTestingBrush}"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding CurrentTestResult}" Value="{x:Static models:TestResultStatus.AwaitingProduct}">
                            <Setter Property="Fill" Value="{StaticResource ProductAwaitingBrush}"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding CurrentTestResult}" Value="{x:Static models:TestResultStatus.None}">
                            <Setter Property="Visibility" Value="Collapsed"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Ellipse.Style>
        </Ellipse>

        <!-- Product code in center -->
        <TextBlock Text="{Binding ProductCode}" 
                   FontWeight="Bold" 
                   FontSize="11"
                   Foreground="Black"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Center" Height="Auto" Width="Auto"/>

    </Grid>
</UserControl>
