using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using System.Windows.Controls;
using ZoomableApp.ViewModels;

namespace ZoomableApp.Views
{
    /// <summary>
    /// Interaction logic for DailyPlanActualChartControl.xaml
    /// </summary>
    public partial class DailyPlanActualChartControl : UserControl
    {
        private DailyPlanActualChartViewModel _viewModel;

        public DailyPlanActualChartControl()
        {
            InitializeComponent();
            DailyPlanChart.LegendTextPaint = new SolidColorPaint(SKColors.White);
            _viewModel = new DailyPlanActualChartViewModel();
            DataContext = _viewModel;
        }
    }
}
