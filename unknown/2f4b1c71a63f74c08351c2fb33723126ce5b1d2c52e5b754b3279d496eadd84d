using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Text.Json;
using ZoomableApp.Models;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Service for managing PLC fault information and mapping
    /// </summary>
    public class PlcFaultService : INotifyPropertyChanged
    {
        private readonly Dictionary<string, PlcFaultInfo> _faultMapping;
        private readonly ObservableCollection<PlcFaultInfo> _activeFaults;
        private PlcFaultInfo _currentMainlineFault;
        private PlcFaultInfo _currentInspectionFault;

        public ObservableCollection<PlcFaultInfo> ActiveFaults => _activeFaults;

        /// <summary>
        /// Current fault for Mainline system
        /// </summary>
        public PlcFaultInfo CurrentMainlineFault
        {
            get => _currentMainlineFault;
            private set
            {
                if (_currentMainlineFault != value)
                {
                    _currentMainlineFault = value;
                    OnPropertyChanged(nameof(CurrentMainlineFault));
                    OnPropertyChanged(nameof(MainlineFaultDisplay));
                    OnPropertyChanged(nameof(MainlineFaultColor));
                }
            }
        }

        /// <summary>
        /// Current fault for Inspection system
        /// </summary>
        public PlcFaultInfo CurrentInspectionFault
        {
            get => _currentInspectionFault;
            private set
            {
                if (_currentInspectionFault != value)
                {
                    _currentInspectionFault = value;
                    OnPropertyChanged(nameof(CurrentInspectionFault));
                    OnPropertyChanged(nameof(InspectionFaultDisplay));
                    OnPropertyChanged(nameof(InspectionFaultColor));
                }
            }
        }

        /// <summary>
        /// Display text for Mainline fault
        /// </summary>
        public string MainlineFaultDisplay => CurrentMainlineFault?.DisplayText ?? "Không có lỗi";

        /// <summary>
        /// Display text for Inspection fault
        /// </summary>
        public string InspectionFaultDisplay => CurrentInspectionFault?.DisplayText ?? "Không có lỗi";

        /// <summary>
        /// Color for Mainline fault display
        /// </summary>
        public string MainlineFaultColor => CurrentMainlineFault?.StatusColor ?? "#4ECDC4";

        /// <summary>
        /// Color for Inspection fault display
        /// </summary>
        public string InspectionFaultColor => CurrentInspectionFault?.StatusColor ?? "#4ECDC4";

        public event PropertyChangedEventHandler? PropertyChanged;

        public PlcFaultService()
        {
            _faultMapping = new Dictionary<string, PlcFaultInfo>();
            _activeFaults = new ObservableCollection<PlcFaultInfo>();
            _currentMainlineFault = new PlcFaultInfo("", new List<int>(), "", false);
            _currentInspectionFault = new PlcFaultInfo("", new List<int>(), "", false);
            
            LoadFaultMapping();
        }

        /// <summary>
        /// Load fault mapping from JSON configuration file
        /// </summary>
        private void LoadFaultMapping()
        {
            try
            {
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "fault_mapping.json");
                
                if (!File.Exists(configPath))
                {
                    // Create default mapping if file doesn't exist
                    CreateDefaultFaultMapping();
                    return;
                }

                string jsonContent = File.ReadAllText(configPath);
                var faultData = JsonSerializer.Deserialize<Dictionary<string, FaultMappingData>>(jsonContent);

                if (faultData != null)
                {
                    _faultMapping.Clear();
                    foreach (var kvp in faultData)
                    {
                        _faultMapping[kvp.Key] = new PlcFaultInfo(kvp.Key, kvp.Value.Stations, kvp.Value.Message, false);
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error and create default mapping
                Console.WriteLine($"Error loading fault mapping: {ex.Message}");
                CreateDefaultFaultMapping();
            }
        }

        /// <summary>
        /// Create default fault mapping if configuration file is not available
        /// </summary>
        private void CreateDefaultFaultMapping()
        {
            _faultMapping.Clear();
            _faultMapping["01"] = new PlcFaultInfo("01", new List<int> { 1 }, "Sensor lỗi", false);
            _faultMapping["02"] = new PlcFaultInfo("02", new List<int> { 2 }, "Quá nhiệt", false);
            _faultMapping["E101"] = new PlcFaultInfo("E101", new List<int> { 1, 2, 3 }, "Tín hiệu yếu", false);
            _faultMapping["Z3"] = new PlcFaultInfo("Z3", new List<int> { 3 }, "Kẹt thùng", false);
        }

        /// <summary>
        /// Process fault data from PLC (smart parsing)
        /// Converts any PLC data to string and maps to fault information
        /// </summary>
        /// <param name="plcData">Raw PLC data (can be number, string, etc.)</param>
        /// <param name="systemType">System type: "mainline" or "inspection"</param>
        public void ProcessFaultData(object? plcData, string systemType)
        {
            if (plcData == null)
            {
                ClearFault(systemType);
                return;
            }

            // Smart parsing: convert everything to string
            string faultCode = plcData.ToString()?.Trim() ?? "";

            // If empty or "0", no fault
            if (string.IsNullOrEmpty(faultCode) || faultCode == "0")
            {
                ClearFault(systemType);
                return;
            }

            // Look up fault in mapping
            if (_faultMapping.TryGetValue(faultCode, out var faultTemplate))
            {
                var activeFault = new PlcFaultInfo(faultCode, faultTemplate.Stations, faultTemplate.Message, true);
                SetFault(activeFault, systemType);
            }
            else
            {
                // Unknown fault code - create generic fault
                var unknownFault = new PlcFaultInfo(faultCode, new List<int>(), $"Lỗi không xác định: {faultCode}", true);
                SetFault(unknownFault, systemType);
            }
        }

        /// <summary>
        /// Set fault for specific system
        /// </summary>
        private void SetFault(PlcFaultInfo fault, string systemType)
        {
            switch (systemType.ToLower())
            {
                case "mainline":
                    CurrentMainlineFault = fault;
                    break;
                case "inspection":
                    CurrentInspectionFault = fault;
                    break;
            }

            // Add to active faults if not already present
            var existingFault = _activeFaults.FirstOrDefault(f => f.FaultCode == fault.FaultCode);
            if (existingFault == null)
            {
                _activeFaults.Add(fault);
            }
            else
            {
                existingFault.IsActive = true;
                existingFault.Timestamp = DateTime.Now;
            }
        }

        /// <summary>
        /// Clear fault for specific system
        /// </summary>
        private void ClearFault(string systemType)
        {
            switch (systemType.ToLower())
            {
                case "mainline":
                    if (CurrentMainlineFault.IsActive)
                    {
                        CurrentMainlineFault = new PlcFaultInfo("", new List<int>(), "", false);
                    }
                    break;
                case "inspection":
                    if (CurrentInspectionFault.IsActive)
                    {
                        CurrentInspectionFault = new PlcFaultInfo("", new List<int>(), "", false);
                    }
                    break;
            }
        }

        /// <summary>
        /// Get fault information by code
        /// </summary>
        public PlcFaultInfo? GetFaultInfo(string faultCode)
        {
            return _faultMapping.TryGetValue(faultCode, out var fault) ? fault : null;
        }

        /// <summary>
        /// Clear all active faults
        /// </summary>
        public void ClearAllFaults()
        {
            CurrentMainlineFault = new PlcFaultInfo("", new List<int>(), "", false);
            CurrentInspectionFault = new PlcFaultInfo("", new List<int>(), "", false);
            _activeFaults.Clear();
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Data structure for JSON deserialization
        /// </summary>
        private class FaultMappingData
        {
            public List<int> Stations { get; set; } = new List<int>();
            public string Message { get; set; } = string.Empty;
        }
    }
}
