<UserControl x:Class="ZoomableApp.Views.SettingsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ZoomableApp.Views"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200">
    
    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>  <!-- Header -->
            <RowDefinition Height="*"/>     <!-- Content -->
        </Grid.RowDefinitions>

        <!-- Header Panel -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="20,15" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="⚙️ Cài đặt Hệ thống" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               Foreground="White" 
                               Margin="0,0,0,5"/>
                    <TextBlock Text="Quản lý cấu hình ứng dụng và kết nối" 
                               FontSize="16" 
                               Foreground="#BDC3C7"/>
                </StackPanel>
                
                <Button Grid.Column="1"
                        x:Name="SaveAllButton"
                        Command="{Binding SaveAllCommand}"
                        Background="#27AE60"
                        Foreground="White"
                        FontSize="16"
                        Style="{StaticResource Modern3DButtonStyle}"
                        Click="SaveAllButton_Click">
                    💾 Lưu tất cả
                </Button>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
            <StackPanel Margin="20">
                
                <!-- Excel Settings Section -->
                <Border Background="White" BorderBrush="#DDD" BorderThickness="1" 
                        CornerRadius="8" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="📊 Cài đặt Excel" FontSize="20" FontWeight="Bold" 
                                   Foreground="#2C3E50" Margin="0,0,0,15"/>
                        
                        <!-- Plan File Path -->
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="File kế hoạch sản xuất:" 
                                       VerticalAlignment="Center" FontWeight="SemiBold"/>
                            <TextBox Grid.Column="1" x:Name="PlanFilePathTextBox" 
                                     Text="{Binding PlanFilePath, Mode=TwoWay}"
                                     Margin="10,0" Padding="8" FontSize="14"/>
                            <Button Grid.Column="2" Content="📁 Chọn file" 
                                    Click="BrowsePlanFile_Click"
                                    Style="{StaticResource Modern3DButtonStyle}"
                                    Background="#3498DB" Foreground="White"/>
                        </Grid>
                        
                        <!-- Maintenance File Path -->
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="File kế hoạch bảo dưỡng:"
                                       VerticalAlignment="Center" FontWeight="SemiBold"/>
                            <TextBox Grid.Column="1" x:Name="MaintenanceFilePathTextBox"
                                     Text="{Binding MaintenancePlanPath, Mode=TwoWay}"
                                     Margin="10,0" Padding="8" FontSize="14"/>
                            <Button Grid.Column="2" Content="📁 Chọn file"
                                    Click="BrowseMaintenanceFile_Click"
                                    Style="{StaticResource Modern3DButtonStyle}"
                                    Background="#3498DB" Foreground="White"/>
                        </Grid>

                        <!-- Idle Time Plans File Path -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="File kế hoạch thời gian nghỉ:"
                                       VerticalAlignment="Center" FontWeight="SemiBold"/>
                            <TextBox Grid.Column="1" x:Name="IdlePlansFilePathTextBox"
                                     Text="{Binding IdlePlansFilePath, Mode=TwoWay}"
                                     Margin="10,0" Padding="8" FontSize="14"/>
                            <Button Grid.Column="2" Content="📁 Chọn file"
                                    Click="BrowseIdlePlansFile_Click"
                                    Style="{StaticResource Modern3DButtonStyle}"
                                    Background="#3498DB" Foreground="White"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- PLC Settings Section -->
                <Border Background="White" BorderBrush="#DDD" BorderThickness="1" 
                        CornerRadius="8" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="🔌 Cài đặt PLC" FontSize="20" FontWeight="Bold" 
                                       Foreground="#2C3E50" VerticalAlignment="Center"/>
                            <Button Grid.Column="1" Content="➕ Thêm PLC" 
                                    Click="AddPlc_Click"
                                    Style="{StaticResource Modern3DButtonStyle}"
                                    Background="#27AE60" Foreground="White"/>
                        </Grid>
                        
                        <!-- PLC List -->
                        <DataGrid x:Name="PlcConfigDataGrid" 
                                  ItemsSource="{Binding PlcConfigs}"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  CanUserReorderColumns="False"
                                  CanUserResizeColumns="True"
                                  CanUserSortColumns="False"
                                  CanUserResizeRows="False"
                                  SelectionMode="Single"
                                  MinHeight="200"
                                  HorizontalAlignment="Stretch"
                                  VerticalAlignment="Stretch">

                            <DataGrid.Style>
                                <Style TargetType="DataGrid">
                                    <Setter Property="Background" Value="White"/>
                                    <Setter Property="BorderThickness" Value="0"/>
                                    <Setter Property="RowBackground" Value="White"/>
                                    <Setter Property="AlternatingRowBackground" Value="#F8F9FA"/>
                                    <Setter Property="GridLinesVisibility" Value="Horizontal"/>
                                    <Setter Property="HorizontalGridLinesBrush" Value="#E9ECEF"/>
                                </Style>
                            </DataGrid.Style>

                            <DataGrid.ColumnHeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#3498DB"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="Padding" Value="10,8"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,0"/>
                                    <Setter Property="BorderBrush" Value="#2980B9"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                </Style>
                            </DataGrid.ColumnHeaderStyle>

                            <DataGrid.CellStyle>
                                <Style TargetType="DataGridCell">
                                    <Setter Property="BorderThickness" Value="0"/>
                                    <Setter Property="Padding" Value="10,6"/>
                                    <Setter Property="FontSize" Value="14"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter Property="Background" Value="#E3F2FD"/>
                                            <Setter Property="Foreground" Value="#1976D2"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.CellStyle>

                            <DataGrid.Columns>
                                <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="120" IsReadOnly="False"/>
                                <DataGridTextColumn Header="Tên hiển thị" Binding="{Binding Name}" Width="150" IsReadOnly="False"/>
                                <DataGridTextColumn Header="Địa chỉ IP" Binding="{Binding IpAddress}" Width="120" IsReadOnly="False"/>
                                <DataGridTextColumn Header="Port" Binding="{Binding Port}" Width="80" IsReadOnly="False"/>
                                <DataGridCheckBoxColumn Header="Tự động kết nối" Binding="{Binding AutoConnect}" Width="120"/>
                                <DataGridCheckBoxColumn Header="Kích hoạt" Binding="{Binding IsEnabled}" Width="100"/>
                                <DataGridTemplateColumn Header="Thao tác" Width="100">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Button Content="🗑️" 
                                                    Click="DeletePlc_Click"
                                                    Style="{StaticResource Modern3DButtonStyle}"
                                                    Background="#E74C3C" Foreground="White"
                                                    Width="30" Height="25" FontSize="12"
                                                    ToolTip="Xóa PLC này"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- General Settings Section -->
                <Border Background="White" BorderBrush="#DDD" BorderThickness="1" 
                        CornerRadius="8" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="🔧 Cài đặt chung" FontSize="20" FontWeight="Bold" 
                                   Foreground="#2C3E50" Margin="0,0,0,15"/>
                        
                        <!-- Database Path -->
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="Thư mục Database:" 
                                       VerticalAlignment="Center" FontWeight="SemiBold"/>
                            <TextBox Grid.Column="1" x:Name="DatabasePathTextBox" 
                                     Text="{Binding DatabasePath, Mode=TwoWay}"
                                     Margin="10,0" Padding="8" FontSize="14"/>
                            <Button Grid.Column="2" Content="📁 Chọn thư mục" 
                                    Click="BrowseDatabaseFolder_Click"
                                    Style="{StaticResource Modern3DButtonStyle}"
                                    Background="#3498DB" Foreground="White"/>
                        </Grid>
                        
                        <!-- Auto Connect PLCs -->
                        <CheckBox x:Name="AutoConnectPlcsCheckBox" 
                                  Content="Tự động kết nối tất cả PLC khi khởi động"
                                  IsChecked="{Binding AutoConnectAllPlcs, Mode=TwoWay}"
                                  FontSize="14" Margin="0,0,0,10"/>
                        
                        <!-- Data Refresh Interval -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="Tần suất đọc dữ liệu (ms):" 
                                       VerticalAlignment="Center" FontWeight="SemiBold"/>
                            <TextBox Grid.Column="1" x:Name="DataRefreshIntervalTextBox" 
                                     Text="{Binding DataRefreshInterval, Mode=TwoWay}"
                                     Margin="10,0" Padding="8" FontSize="14"/>
                            <TextBlock Grid.Column="2" Text="(Khuyến nghị: 1000-5000ms)" 
                                       VerticalAlignment="Center" FontStyle="Italic" Foreground="Gray"/>
                        </Grid>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
