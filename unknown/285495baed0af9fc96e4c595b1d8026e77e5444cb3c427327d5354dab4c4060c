﻿using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace ZoomableApp.Views
{
    /// <summary>
    /// Interaction logic for DailyQualityChartControl.xaml
    /// </summary>
    public partial class DailyQualityChartControl : UserControl
    {
        public DailyQualityChartControl()
        {
            InitializeComponent();
            // Set DataContext to the ViewModel
            DailyQualityChart.LegendTextPaint = new SolidColorPaint(SKColors.White);
            this.DataContext = new ViewModels.DailyQualityChartViewModel();
        }
    }
}
