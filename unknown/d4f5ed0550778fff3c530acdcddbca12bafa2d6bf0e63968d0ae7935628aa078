# 🏭 ZoomableApp - <PERSON><PERSON> thống Quản lý Sản xuất

## 📋 Tổng quan dự án

**ZoomableApp** là một ứng dụng WPF hiện đại để quản lý và theo dõi sản xuất với các tính năng:

- **🔐 Hệ thống đăng nhập** với RFID và tài khoản
- **📊 Trang Kế hoạch** tích hợp Excel tự động
- **🎛️ Giao diện zoomable** với sidebar navigation
- **🔌 Kết nối PLC** Mitsubishi (tùy chọn)
- **💾 Database SQLite** cho quản lý người dùng

## 🏗️ Kiến trúc dự án

### **📁 Cấu trúc thư mục:**
```
ZoomableApp/
├── Views/                  # UI Pages (UserControls)
│   ├── PlanPage.xaml      # Trang Kế hoạch Excel
│   └── PlanPage.xaml.cs
├── ViewModels/            # MVVM ViewModels
│   ├── PlanViewModel.cs   # Logic cho trang Kế hoạch
│   └── PlcConnectionUIData.cs
├── Services/              # Business Logic
│   ├── ExcelService.cs    # Xử lý Excel với ClosedXML
│   ├── ConfigLoader.cs    # Đọc config từ JSON
│   ├── UserService.cs     # Quản lý người dùng
│   └── DatabaseInitializer.cs
├── Models/                # Data Models
│   ├── User.cs           # Model người dùng
│   ├── WorkShift.cs      # Model ca làm việc
│   └── UserSession.cs    # Session management
├── Data/                 # Database
│   └── panaDB.db        # SQLite database
├── Layouts/              # Layout controls
├── SharedControls/       # Shared UI components
└── PLC/                 # PLC integration (optional)
```

### **🎨 UI Architecture:**
- **MainWindow**: Container chính với sidebar navigation
- **Views**: Các trang riêng biệt (UserControl)
- **ViewModels**: MVVM pattern cho separation of concerns
- **Professional styling**: Modern UI với color scheme nhất quán

## 🔐 Hệ thống Đăng nhập

### **✨ Tính năng:**
- **2 phương thức đăng nhập**: RFID card và tài khoản
- **Hỗ trợ đầu đọc HID Omnikey** (xem `RFID_INTEGRATION.md`)
- **4 ca làm việc**: Hành chính (8-17h), Sáng (6-11h), Chiều (14-22h), Đêm (22-6h)
- **SQLite database**: Lưu trữ thông tin người dùng
- **Session management**: Truyền thông tin user qua các trang

### **🗄️ Database Schema:**
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    role TEXT NOT NULL,
    fullname TEXT NOT NULL
);
```

### **👤 Sample Users:**
| Username | Password | Role | Full Name |
|----------|----------|------|-----------|
| admin | admin123 | Admin | Quản trị viên |
| user1 | user123 | User | Nguyễn Văn A |
| operator1 | op123 | Operator | Trần Thị B |

### **🧪 Test Login:**
1. **Chạy ứng dụng**: `dotnet run`
2. **Chọn tab "Tài khoản"**
3. **Nhập**: username: `admin`, password: `admin123`
4. **Chọn ca làm việc** và đăng nhập
5. **Kiểm tra**: Sidebar hiển thị thông tin user

## 📊 Trang Kế hoạch Excel

### **🎯 Tính năng chính:**
- **Tự động load Excel** khi vào trang
- **ClosedXML integration** (hoàn toàn miễn phí!)
- **Professional DataGrid** với styling đẹp
- **Real-time refresh** với nút "🔄 Làm mới"
- **Error handling** robust với thông báo chi tiết
- **File validation** trước khi đọc

### **⚙️ Configuration:**
File `appsettings.json`:
```json
{
  "ExcelSettings": {
    "PlanFilePath": "E:\\Project-Dat\\test.xlsx"
  }
}
```

### **📋 Tạo file Excel mẫu:**
Tạo file `E:\Project-Dat\test.xlsx` với cấu trúc:

| Mã sản phẩm | Tên sản phẩm | Số lượng | Ngày bắt đầu | Ngày kết thúc | Trạng thái |
|-------------|--------------|----------|--------------|---------------|------------|
| SP001 | Sản phẩm A | 100 | 2024-01-15 | 2024-01-20 | Đang thực hiện |
| SP002 | Sản phẩm B | 200 | 2024-01-16 | 2024-01-25 | Chờ nguyên liệu |
| SP003 | Sản phẩm C | 150 | 2024-01-18 | 2024-01-22 | Hoàn thành |

### **🧪 Test Excel tính năng:**
1. **Tạo file Excel** theo mẫu trên
2. **Đăng nhập** vào hệ thống
3. **Chuyển đến trang "📋 Kế hoạch"**
4. **Kiểm tra**: Dữ liệu tự động hiển thị
5. **Test refresh**: Click "🔄 Làm mới"

## 🎨 UI/UX Features

### **🎯 Design Principles:**
- **Modern flat design** với color scheme nhất quán
- **Professional typography** (Segoe UI, font size 16px+)
- **Hover effects** và smooth animations
- **Responsive layout** với proper spacing
- **Accessibility** với clear visual hierarchy

### **🎨 Color Palette:**
- **Primary**: `#2C3E50` (Dark blue-gray)
- **Secondary**: `#3498DB` (Blue)
- **Success**: `#27AE60` (Green)
- **Background**: `#F8F9FA` (Light gray)
- **Text**: `#2C3E50` (Dark)

### **📱 Layout Structure:**
- **Fixed header** với sidebar toggle và page title
- **Sidebar navigation** với user info panel
- **Main content area** responsive
- **Status bars** với real-time updates

## 🔧 Technical Stack

### **🛠️ Dependencies:**
```xml
<PackageReference Include="ClosedXML" Version="0.105.0" />
<PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5" />
<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.5" />
<PackageReference Include="System.Data.SQLite" Version="1.0.118" />
```

### **🏗️ Architecture Patterns:**
- **MVVM Pattern**: ViewModels cho business logic
- **Service Layer**: Separation of concerns
- **Configuration-driven**: appsettings.json cho settings
- **Dependency Injection**: Service registration
- **Error Handling**: Comprehensive exception management

### **💡 Key Technologies:**
- **WPF .NET**: Modern desktop framework
- **ClosedXML**: Free Excel processing (no license issues!)
- **SQLite**: Lightweight database
- **JSON Configuration**: Flexible settings management
- **XAML Styling**: Professional UI design

## 🚀 Hướng dẫn chạy dự án

### **📋 Prerequisites:**
- **.NET 6.0 SDK** hoặc cao hơn
- **Visual Studio 2022** hoặc VS Code
- **Windows OS** (WPF requirement)

### **⚡ Quick Start:**
```bash
# Clone repository
git clone <repository-url>
cd ZoomableApp

# Restore packages
dotnet restore

# Build project
dotnet build

# Run application
dotnet run
```

### **🧪 Testing Workflow:**
1. **Login Test**: Sử dụng admin/admin123
2. **Navigation Test**: Click các menu sidebar
3. **Excel Test**: Tạo file Excel và test trang Kế hoạch
4. **UI Test**: Kiểm tra responsive và hover effects

## 📝 Development Notes

### **✅ Completed Features:**
- ✅ **Login system** với database integration
- ✅ **Sidebar navigation** với user session
- ✅ **Excel integration** với ClosedXML (miễn phí!)
- ✅ **MVVM architecture** với ViewModels
- ✅ **Professional UI/UX** design
- ✅ **Configuration system** với JSON
- ✅ **Error handling** comprehensive

### **🔄 Future Enhancements:**
- **More pages**: Thêm các trang quản lý khác
- **Real-time updates**: WebSocket cho live data
- **Export features**: PDF, CSV export
- **Advanced filtering**: Search và filter trong DataGrid
- **User management**: CRUD operations cho users
- **Audit logging**: Track user activities

### **🐛 Known Issues:**
- **PLC integration**: Optional, có thể disable nếu không cần
- **File paths**: Cần absolute paths trong config
- **Database**: Tự tạo nếu không tồn tại

### **💡 Best Practices:**
- **Always use package managers** thay vì edit package files
- **Prefer free/open-source** tools over licensed ones
- **MVVM pattern** cho maintainable code
- **Configuration-driven** development
- **Comprehensive error handling**

## 📞 Support & Contact

Dự án được phát triển với focus vào:
- **Free/open-source tools** (ClosedXML thay vì EPPlus)
- **Professional UI/UX** design
- **Maintainable architecture** với MVVM
- **Comprehensive documentation**

Hãy tạo file Excel mẫu và test các tính năng! 🚀

## 🔄 Recent Updates & Refactoring

### ✅ **MVVM Architecture Implementation (Latest)**

#### **📁 New Structure:**
- **Views/PlanPage.xaml** - UserControl cho trang Kế hoạch
- **ViewModels/PlanViewModel.cs** - MVVM ViewModel với Excel logic
- **Separation of Concerns** - UI logic tách khỏi MainWindow

#### **🎨 UI/UX Improvements:**
- **Font size increased** từ 11-12px lên **16px** cho better readability
- **Header font** tăng lên **24px** cho professional appearance
- **Consistent typography** across all UI elements
- **Better accessibility** và user experience

#### **🏗️ Technical Benefits:**
- **MVVM Pattern** correctly implemented
- **Data Binding** với INotifyPropertyChanged
- **RelayCommand** cho button interactions
- **Testable ViewModels** cho future unit testing
- **Clean Architecture** với proper separation

#### **📋 Changes Made:**
- ✅ **Tách PlanPage** thành UserControl riêng biệt
- ✅ **Tạo PlanViewModel** với Excel logic hoàn chỉnh
- ✅ **Tăng font size** cho better readability
- ✅ **Gộp documentation** vào README.md
- ✅ **Clean workspace** - xóa các file .md cũ

### 🧪 **Testing After Refactoring:**
1. **Build successful** - No errors, chỉ nullability warnings
2. **Runtime stable** - Tất cả functionality hoạt động
3. **MVVM binding** - Data binding perfect
4. **UI improvements** - Font size lớn hơn, đẹp hơn
5. **Excel integration** - Auto-load và refresh vẫn hoạt động

### 📝 **Development Notes:**
- **MVVM pattern** giúp code maintainable và testable hơn
- **Font size 16px** cải thiện đáng kể user experience
- **Clean documentation** trong single README.md file
- **Future enhancements** có thể dễ dàng implement với architecture mới

### ✅ **Excel Integration Enhancements (Latest)**

#### **🔧 Leading Zeros Preservation:**
- **Fixed Excel reading** để giữ nguyên leading zeros (0530 không bị thành 530)
- **Enhanced ExcelService** với logic xử lý format đặc biệt
- **Support multiple formats** cho cells (Text, Number, Formula)

#### **📊 Plan Page Split Design:**
- **Kế hoạch Tháng** (phía trên) - Hiển thị toàn bộ dữ liệu Excel
- **Kế hoạch Hôm nay** (phía dưới) - Auto-extract từ monthly data
- **Smart date matching** với multiple format support (dd/MM, dd/MM/yyyy)
- **Real-time filtering** dựa trên ngày hiện tại

#### **🎨 Responsive DataGrid Design:**
- **Minimum size**: 800x400px với scrollbars khi cần
- **Auto-expand** khi window lớn hơn
- **Professional styling** với colors khác nhau cho Monthly/Daily
- **Better UX** với hover effects và proper spacing

#### **📅 Daily Plan Features:**
- **Auto-detect Date column** từ Excel headers
- **Today's date display** trong header với format (dd/MM/yyyy)
- **Empty state handling** - "Hôm nay không có kế hoạch" khi không có data
- **Status bar updates** hiển thị count cho cả Monthly và Daily

#### **🔍 Technical Improvements:**
- **Enhanced error handling** cho date parsing
- **Multiple date format support** (dd/MM/yyyy, dd/MM, auto-parse)
- **Memory efficient** với proper DataTable cloning
- **Debug logging** cho troubleshooting

### 🧪 **Testing Results (Excel Integration):**
1. **Leading zeros preserved** - 0530 hiển thị đúng thay vì 530
2. **Plan page split** - Monthly và Daily sections hoạt động perfect
3. **Date filtering** - Auto-extract daily plan từ current date
4. **Responsive design** - DataGrid auto-resize với scrollbars
5. **Professional UI** - Colors và styling nhất quán

### ✅ **Responsive Design Optimization (Latest)**

#### **🎯 Fixed MinHeight/MinWidth Issues:**
- **Removed excessive ScrollViewer MinWidth** (1000px → Auto)
- **Optimized DataGrid MinWidth** từ 800px xuống 600px cho better fit
- **Smart Grid RowDefinitions** với MinHeight và MaxHeight constraints
- **Proper scrollbar behavior** - Chỉ hiện khi thực sự cần

#### **📐 Layout Improvements:**
- **Monthly section**: MinHeight 250px, MaxHeight 500px
- **Daily section**: MinHeight 200px, MaxHeight 400px
- **Balanced space distribution** với Height="*" và constraints
- **Responsive behavior** - Auto-adjust theo window size

#### **🎨 Better User Experience:**
- **Scrollbars appear** khi content vượt quá available space
- **No unnecessary scrollbars** khi content fit trong window
- **Smooth resizing** khi user thay đổi window size
- **Professional layout** với proper minimum sizes

#### **🔧 Technical Optimizations:**
- **Removed redundant MinWidth** từ ScrollViewer
- **Optimized DataGrid constraints** cho better performance
- **Smart Grid sizing** với MinHeight/MaxHeight balance
- **Clean XAML structure** dễ maintain và extend

### 🧪 **Testing Results (Responsive Design):**
1. **Minimum size enforcement** ✅ - Sections có minimum height hợp lý
2. **Scrollbars when needed** ✅ - Auto-appear khi content lớn
3. **No excessive scrollbars** ✅ - Không hiện khi không cần
4. **Window resizing** ✅ - Smooth adaptation theo window size
5. **Professional appearance** ✅ - Layout balanced và đẹp mắt

### 🏠 **Homepage Daily Plan Integration (Latest)**

#### **🎯 Major Layout Redesign:**
- **Layout area reduced** từ 2* xuống 1* (chiều cao 1/3)
- **Bottom area expanded** thành 2* (chiều cao 2/3)
- **Div1 & Div2 merged** thành Daily Plan section (2/3 width)
- **Div3 & Div4 preserved** cho future development (1/3 width)
- **Professional daily plan display** với real-time status management
- **Optimized space utilization** cho better user experience

#### **📊 Daily Plan Features:**

##### **🎨 Status Color System:**
- **🟡 Màu vàng (Gold)**: Model đang làm (In Progress)
- **🟢 Màu xanh lá (Green)**: Model đã hoàn thành (Completed)
- **🔵 Màu xanh dương (Blue)**: Model được chọn (Selected) - chỉ cho models chưa hoàn thành
- **⚪ Màu nền bình thường**: Model chưa bắt đầu (Not Started)

##### **🎮 Interactive Controls:**
- **🔄 Refresh Button**: Cập nhật kế hoạch mới nhất từ Excel
- **✅ Done Button**: Chuyển model đang làm (vàng) → hoàn thành (xanh lá)
- **▶️ Next Button**: Chuyển model được chọn (xanh dương) → đang làm (vàng)
- **📅 Date Display**: Hiển thị ngày hiện tại
- **🏷️ Current Product**: Hiển thị sản phẩm đang sản xuất

##### **🔒 Smart Selection Logic:**
- **Completed models**: Không thể chọn (disabled)
- **In-progress models**: Không thể chọn (disabled)
- **Not started models**: Có thể chọn để làm tiếp theo
- **Auto-selection prevention**: Ngăn chọn models đã hoàn thành/đang làm

#### **🏗️ Technical Implementation:**

##### **📁 New Components:**
```
ViewModels/
├── HomeDailyPlanViewModel.cs    # Main ViewModel cho Daily Plan
├── DailyPlanItem.cs            # Model cho từng plan item
└── PlanItemStatus.cs           # Enum cho status management

Converters/
├── PlanItemStatusToColorConverter.cs      # Convert status → background color
└── PlanItemStatusToTextColorConverter.cs  # Convert status → text color
```

##### **🎯 Key Features:**
- **Real-time Excel integration** - Auto-load daily plan từ Excel file
- **Date filtering** - Chỉ hiển thị plan của ngày hiện tại
- **Status persistence** - Maintain status khi refresh
- **Command pattern** - Clean MVVM implementation với ICommand
- **Responsive DataGrid** - Auto-resize với proper scrollbars

#### **📐 Layout Optimization:**
- **Top area (1/3)**: Zoom/Pan layout controls và viewport
- **Bottom area (2/3)**: Split thành Daily Plan (2/3) + Div3/Div4 (1/3)
- **Daily Plan section**: Header với controls + scrollable DataGrid
- **Future development areas**: Div3 và Div4 preserved cho tính năng tương lai
- **Professional styling**: Consistent với design system

### 🧪 **Testing Results (Daily Plan Integration):**
1. **Layout proportions** ✅ - Top 1/3, Bottom 2/3 perfect
2. **Daily plan display** ✅ - Load và hiển thị plan hôm nay (2/3 width)
3. **Div3/Div4 preserved** ✅ - Future development areas (1/3 width)
4. **Status colors** ✅ - Vàng/Xanh lá/Xanh dương/Bình thường
5. **Interactive buttons** ✅ - Refresh/Done/Next hoạt động
6. **Selection logic** ✅ - Chỉ cho phép chọn models chưa hoàn thành
7. **Excel integration** ✅ - Auto-extract daily plan từ monthly data
8. **Professional UI** ✅ - Clean, intuitive, và responsive
9. **Space optimization** ✅ - Efficient use của available space

### 🎮 **Button Press Effects & Debug Enhancement (Latest)**

#### **🎯 Button Visual Feedback:**
- **Press animation**: Scale down to 95% khi nhấn với smooth transition
- **Hover effect**: Opacity 80% khi mouse over
- **Round button style**: Consistent 17px corner radius
- **Smooth transitions**: 0.1s duration cho natural feel

#### **🔧 Debug Implementation:**
- **Click event handlers**: Debug logging cho tất cả 3 buttons
- **Command binding**: Dual approach với Command + Click events
- **Console output**: Track button interactions trong debug mode
- **Error detection**: Identify refresh button issues

#### **🎨 Enhanced Button Styling:**
```xml
<Style x:Key="RoundButtonStyle" TargetType="Button">
    <!-- Press Effect: Scale 95% -->
    <!-- Hover Effect: Opacity 80% -->
    <!-- Round Corners: 17px radius -->
    <!-- Smooth Animations: 0.1s duration -->
</Style>
```

#### **🐛 Refresh Button Debug:**
- **Added debug logging** trong LoadDailyPlan method
- **Click event tracking** để verify button interactions
- **Command execution monitoring** cho troubleshooting
- **Excel file validation** với detailed error messages

### 🧪 **Testing Results (Button Effects & Debug):**
1. **Press animations** ✅ - Buttons scale down khi nhấn
2. **Hover effects** ✅ - Opacity changes on mouse over
3. **Visual feedback** ✅ - Clear indication của button interactions
4. **Debug logging** ✅ - Console output cho button clicks
5. **Refresh functionality** ✅ - Now syncs with Plan page data

### 🔄 **Data Synchronization Implementation (Latest)**

#### **🎯 Shared Data Architecture:**
- **Single PlanViewModel instance** shared giữa Plan page và Homepage
- **Real-time data sync** thông qua PropertyChanged events
- **Centralized Excel processing** trong PlanViewModel
- **Automatic daily plan extraction** từ monthly data

#### **🔧 Technical Implementation:**

##### **1. Shared ViewModel Pattern:**
```csharp
// MainWindow creates shared instance
_planViewModel = new PlanViewModel();

// Homepage uses shared instance
_dailyPlanViewModel = new HomeDailyPlanViewModel(_planViewModel);

// Plan page uses same instance
planPageContent.DataContext = _planViewModel;
```

##### **2. Data Flow Architecture:**
```
Excel File → PlanViewModel.LoadExcelData()
          ↓
     MonthlyPlanData (full Excel data)
          ↓
     ExtractDailyPlan() (filter by today)
          ↓
     DailyPlanData (today's plan only)
          ↓
     PropertyChanged event
          ↓
     HomeDailyPlanViewModel.LoadDailyPlanFromPlanViewModel()
          ↓
     Homepage Daily Plan Display
```

##### **3. Refresh Mechanism:**
- **Plan page refresh** → Updates shared PlanViewModel
- **Homepage refresh** → Triggers PlanViewModel.RefreshCommand
- **Automatic sync** → PropertyChanged events update Homepage
- **Real-time updates** → Both pages stay synchronized

#### **🎮 Enhanced Button Functionality:**
- **Refresh button** → Syncs với Plan page data
- **Visual feedback** → Press animations working
- **Debug logging** → Console tracking for troubleshooting
- **Command execution** → Proper MVVM pattern implementation

### 🧪 **Testing Results (Data Sync):**
1. **Shared data model** ✅ - Single PlanViewModel instance
2. **Real-time sync** ✅ - Homepage updates khi Plan page changes
3. **Refresh functionality** ✅ - Button triggers data reload
4. **Excel integration** ✅ - Centralized processing
5. **Daily plan extraction** ✅ - Auto-filter today's data
6. **Button animations** ✅ - Visual feedback working
7. **Debug infrastructure** ✅ - Console logging active

---

## 🔧 **Maintenance Management System (Latest)**

### 🎯 **Maintenance Page Features:**

#### **📋 Core Functionality:**
- **Maintenance scheduling** với automatic reminder system
- **Overdue notifications** khi qua ngày bảo dưỡng
- **Maintenance execution** với result recording
- **History tracking** với date range filtering
- **Excel integration** cho data import/export
- **Status-based color coding** (Normal/Due Soon/Overdue)

#### **🎨 Professional UI Design:**
- **Dark theme** (#2C3E50 background) matching app design
- **Color-coded status rows** (Green/Orange/Red)
- **Modern button styling** với hover/press animations
- **Responsive layout** với left panel và main data grid
- **Modal dialog** cho maintenance result input
- **Professional typography** với white text on dark background

#### **⚙️ Technical Implementation:**

##### **1. Data Models:**
```csharp
public class MaintenanceItem
{
    public int Id { get; set; }
    public string Content { get; set; }        // Nội dung bảo dưỡng
    public string Method { get; set; }         // Phương pháp thực hiện
    public int Cycle { get; set; }             // Chu kỳ (ngày)
    public string Guide { get; set; }          // Hướng dẫn chi tiết
    public DateTime LastMaintenanceDate { get; set; }
    public DateTime NextMaintenanceDate { get; set; }
    public MaintenanceStatus Status { get; set; }
    public string Result { get; set; }         // Kết quả bảo dưỡng
    public string PerformedBy { get; set; }    // Người thực hiện
}

public enum MaintenanceStatus
{
    Normal,     // Bình thường - màu xanh lá
    DueSoon,    // Sắp đến hạn - màu cam
    Overdue     // Quá hạn - màu đỏ
}
```

##### **2. Smart Status Management:**
- **Automatic status calculation** based on dates
- **7-day warning period** cho DueSoon status
- **Real-time status updates** khi dates change
- **Visual indicators** với color-coded rows

##### **3. Maintenance Workflow:**
```
1. Select maintenance item → Enable "XÁC NHẬN" button
2. Click "XÁC NHẬN" → Open maintenance dialog
3. Enter maintenance result → Click "Xác nhận"
4. System updates:
   - LastMaintenanceDate = Today
   - NextMaintenanceDate = Today + Cycle
   - Status = Normal
   - Result = User input
   - PerformedBy = Current user
```

#### **🎮 Interactive Features:**

##### **1. Button Functions:**
- **Tổng hợp** → Refresh all maintenance data
- **Trạng thái** → Filter by maintenance status
- **Lịch sử** → View history trong date range
- **XÁC NHẬN** → Perform maintenance (enabled when item selected)
- **Xuất Excel** → Export current view to Excel

##### **2. Date Range Filtering:**
- **From/To DatePickers** cho history filtering
- **Default range** = current month
- **Real-time filtering** khi dates change

##### **3. Maintenance Dialog:**
- **Modal overlay** với professional styling
- **Multi-line text input** cho detailed results
- **Confirm/Cancel buttons** với proper validation
- **Auto-close** after successful submission

#### **📊 Sample Data Structure:**
```
ID | Content              | Method                    | Cycle | Guide                           | Last       | Next
1  | Bôi áp suất khí 0.4-0.6 | Quan sát đồng hồ áp suất | 1     | Điều chỉnh áp suất khí nếu...  | 2024-06-01 | 2024-06-02
2  | Bạc bi trục trượt    | Dùng tay kiểm tra độ cứng | 3     | Kết trục bạc bi: Ngắt khí...   | 2024-05-29 | 2024-06-01
3  | Bu lông              | Dùng cờ lê bộ lục kiểm tra | 1     | Kết thêm đầu, mở ra đầu mỏ     | 2024-06-02 | 2024-06-03
```

### 🧪 **Testing Results (Maintenance System):**
1. **UI Design** ✅ - Professional dark theme matching app
2. **Data binding** ✅ - MVVM pattern với ObservableCollection
3. **Status calculation** ✅ - Automatic Normal/DueSoon/Overdue
4. **Color coding** ✅ - Green/Orange/Red row backgrounds
5. **Button interactions** ✅ - Hover/press animations working
6. **Modal dialog** ✅ - Maintenance result input functional
7. **Date filtering** ✅ - History view với date range
8. **Sample data** ✅ - 6 realistic maintenance items loaded
9. **Command binding** ✅ - All buttons connected to ViewModels
10. **Responsive layout** ✅ - Left panel + main grid design
