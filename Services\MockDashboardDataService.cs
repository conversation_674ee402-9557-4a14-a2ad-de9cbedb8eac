using System;
using System.Collections.Generic;
using System.Linq;
using ZoomableApp.Models;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Service cung cấp mock data cho tất cả dashboard charts
    /// Tạo dữ liệu realistic và consistent cho demo/testing
    /// </summary>
    public class MockDashboardDataService
    {
        private readonly Random _random = new Random();
        private static readonly string[] StationNames = {
            "ST1", "ST2", "ST3", "ST4", "ST5", "ST6", "ST7", "ST8", "ST9", "ST10",
            "ST11", "ST12", "ST13", "ST14", "ST15", "ST16", "ST17", "ST18"
        };

        /// <summary>
        /// Tạo mock data cho Stop Times Chart
        /// </summary>
        public (string Station, int StopCount)[] GetMockStopTimesData(int topN = 5)
        {
            var result = new List<(string Station, int StopCount)>();
            var isRandomVariationEnabled = ConfigurationService.IsRandomVariationEnabled();

            for (int i = 0; i < Math.Min(topN, StationNames.Length); i++)
            {
                int stopCount;
                
                if (isRandomVariationEnabled)
                {
                    // Realistic distribution: một số station có vấn đề nhiều hơn
                    if (i < 2) // Top 2 problematic stations
                    {
                        stopCount = _random.Next(8, 15);
                    }
                    else if (i < 4) // Medium problematic stations
                    {
                        stopCount = _random.Next(3, 8);
                    }
                    else // Good performing stations
                    {
                        stopCount = _random.Next(0, 4);
                    }
                }
                else
                {
                    // Static decreasing pattern for consistent demo
                    stopCount = Math.Max(0, 12 - i * 2);
                }

                result.Add((StationNames[i], stopCount));
            }

            // Sort by stop count descending
            return result.OrderByDescending(x => x.StopCount).ToArray();
        }

        /// <summary>
        /// Tạo mock data cho Production Quality Charts (OK/NG/Rework)
        /// </summary>
        public (int OK, int NG, int Rework) GetMockQualityData(bool isCurrentShift = false)
        {
            var isRandomVariationEnabled = ConfigurationService.IsRandomVariationEnabled();

            if (isRandomVariationEnabled)
            {
                // Realistic production ratios
                var total = isCurrentShift ? _random.Next(50, 200) : _random.Next(200, 500);
                var ngRate = _random.NextDouble() * 0.05; // 0-5% NG rate
                var reworkRate = _random.NextDouble() * 0.03; // 0-3% Rework rate
                
                var ng = (int)(total * ngRate);
                var rework = (int)(total * reworkRate);
                var ok = total - ng - rework;

                return (Math.Max(0, ok), ng, rework);
            }
            else
            {
                // Static data for consistent demo
                if (isCurrentShift)
                {
                    return (85, 3, 2); // Current shift smaller numbers
                }
                else
                {
                    return (320, 12, 8); // Daily larger numbers
                }
            }
        }

        /// <summary>
        /// Tạo mock data cho Production Quality Charts for today (OK/NG/Rework)
        /// </summary>
        public (int OK, int NG, int Rework) GetMockTodayQualityData()
        {
            var isRandomVariationEnabled = ConfigurationService.IsRandomVariationEnabled();
            if (isRandomVariationEnabled)
            {
                // Realistic production ratios for today
                var total = _random.Next(200, 500);
                var ngRate = _random.NextDouble() * 0.05; // 0-5% NG rate
                var reworkRate = _random.NextDouble() * 0.03; // 0-3% Rework rate

                var ng = (int)(total * ngRate);
                var rework = (int)(total * reworkRate);
                var ok = total - ng - rework;
                return (Math.Max(0, ok), ng, rework);
            }
            else
            {
                // Static data for consistent demo
                return (350, 10, 5); // Daily larger numbers
            }
        }
        /// <summary>
        /// Tạo mock data cho Plan vs Actual Charts
        /// </summary>
        public (int Plan, int Actual, int Gap) GetMockPlanActualData(bool isCurrentShift = false)
        {
            var isRandomVariationEnabled = ConfigurationService.IsRandomVariationEnabled();

            if (isRandomVariationEnabled)
            {
                var plan = isCurrentShift ? _random.Next(80, 120) : _random.Next(300, 450);
                var actualRate = 0.85 + _random.NextDouble() * 0.3; // 85-115% of plan
                var actual = (int)(plan * actualRate);
                var gap = actual - plan;

                return (plan, actual, gap);
            }
            else
            {
                // Static data for consistent demo
                if (isCurrentShift)
                {
                    return (100, 92, -8); // Current shift: slightly behind
                }
                else
                {
                    return (400, 385, -15); // Daily: behind plan
                }
            }
        }

        /// <summary>
        /// Tạo mock data cho Idle Hours Charts
        /// </summary>
        public (double IdleHours, double TotalHours, double UtilizationRate) GetMockIdleHoursData(bool isDaily = true)
        {
            var isRandomVariationEnabled = ConfigurationService.IsRandomVariationEnabled();

            if (isRandomVariationEnabled)
            {
                var totalHours = isDaily ? 24.0 : DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month) * 24.0;
                var utilizationRate = 0.75 + _random.NextDouble() * 0.2; // 75-95% utilization
                var workingHours = totalHours * utilizationRate;
                var idleHours = totalHours - workingHours;

                return (Math.Round(idleHours, 1), totalHours, Math.Round(utilizationRate * 100, 1));
            }
            else
            {
                // Static data for consistent demo
                if (isDaily)
                {
                    return (3.2, 24.0, 86.7); // Daily: 3.2h idle, 86.7% utilization
                }
                else
                {
                    var daysInMonth = DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month);
                    var totalHours = daysInMonth * 24.0;
                    var idleHours = totalHours * 0.15; // 15% idle time
                    return (Math.Round(idleHours, 1), totalHours, 85.0);
                }
            }
        }

        /// <summary>
        /// Tạo mock data cho Station Performance
        /// </summary>
        public Dictionary<string, double> GetMockStationPerformance()
        {
            var result = new Dictionary<string, double>();
            var isRandomVariationEnabled = ConfigurationService.IsRandomVariationEnabled();

            foreach (var station in StationNames.Take(10)) // Top 10 stations
            {
                double performance;
                
                if (isRandomVariationEnabled)
                {
                    // Realistic performance distribution
                    performance = 70 + _random.NextDouble() * 25; // 70-95% performance
                }
                else
                {
                    // Static decreasing pattern
                    var index = Array.IndexOf(StationNames, station);
                    performance = 95 - (index * 2.5); // Decreasing from 95% to 70%
                }

                result[station] = Math.Round(performance, 1);
            }

            return result;
        }

        /// <summary>
        /// Tạo mock data cho Error/Alarm trends
        /// </summary>
        public List<(DateTime Time, int ErrorCount, string ErrorType)> GetMockErrorTrends(int hours = 24)
        {
            var result = new List<(DateTime Time, int ErrorCount, string ErrorType)>();
            var errorTypes = new[] { "Sensor Error", "Motor Fault", "Communication", "Safety", "Quality" };
            var isRandomVariationEnabled = ConfigurationService.IsRandomVariationEnabled();

            var startTime = DateTime.Now.AddHours(-hours);
            
            for (int i = 0; i < hours; i++)
            {
                var time = startTime.AddHours(i);
                
                if (isRandomVariationEnabled)
                {
                    // Random errors with some patterns (more errors during shift changes)
                    var isShiftChange = time.Hour == 6 || time.Hour == 14 || time.Hour == 22;
                    var baseErrorRate = isShiftChange ? 0.3 : 0.1;
                    
                    if (_random.NextDouble() < baseErrorRate)
                    {
                        var errorType = errorTypes[_random.Next(errorTypes.Length)];
                        var errorCount = _random.Next(1, 4);
                        result.Add((time, errorCount, errorType));
                    }
                }
                else
                {
                    // Static pattern: errors every 6 hours
                    if (i % 6 == 0 && i > 0)
                    {
                        var errorType = errorTypes[i / 6 % errorTypes.Length];
                        result.Add((time, 1, errorType));
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// Tạo mock data cho Production Trends
        /// </summary>
        public List<(DateTime Time, int Production, double Efficiency)> GetMockProductionTrends(int days = 7)
        {
            var result = new List<(DateTime Time, int Production, double Efficiency)>();
            var isRandomVariationEnabled = ConfigurationService.IsRandomVariationEnabled();

            var startDate = DateTime.Today.AddDays(-days);

            for (int i = 0; i < days; i++)
            {
                var date = startDate.AddDays(i);
                
                if (isRandomVariationEnabled)
                {
                    // Weekend có production thấp hơn
                    var isWeekend = date.DayOfWeek == DayOfWeek.Saturday || date.DayOfWeek == DayOfWeek.Sunday;
                    var baseProduction = isWeekend ? 200 : 400;
                    var variation = _random.Next(-50, 51); // ±50 variation
                    
                    var production = Math.Max(0, baseProduction + variation);
                    var efficiency = 75 + _random.NextDouble() * 20; // 75-95% efficiency
                    
                    result.Add((date, production, Math.Round(efficiency, 1)));
                }
                else
                {
                    // Static pattern with weekly cycle
                    var isWeekend = date.DayOfWeek == DayOfWeek.Saturday || date.DayOfWeek == DayOfWeek.Sunday;
                    var production = isWeekend ? 250 : 380 + (i % 3) * 20; // Slight variation
                    var efficiency = isWeekend ? 70.0 : 85.0 + (i % 3) * 2.5;
                    
                    result.Add((date, production, efficiency));
                }
            }

            return result;
        }

        /// <summary>
        /// Lấy thông tin tổng quan cho dashboard summary
        /// </summary>
        public DashboardSummary GetMockDashboardSummary()
        {
            var qualityData = GetMockQualityData(false);
            var planActualData = GetMockPlanActualData(false);
            var idleData = GetMockIdleHoursData(true);

            return new DashboardSummary
            {
                TotalProduction = qualityData.OK + qualityData.NG + qualityData.Rework,
                QualityRate = Math.Round((double)qualityData.OK / (qualityData.OK + qualityData.NG + qualityData.Rework) * 100, 1),
                PlanAchievement = Math.Round((double)planActualData.Actual / planActualData.Plan * 100, 1),
                UtilizationRate = idleData.UtilizationRate,
                ActiveStations = StationNames.Take(_random.Next(15, 18)).Count(),
                TotalStations = StationNames.Length,
                LastUpdated = DateTime.Now
            };
        }
    }

    /// <summary>
    /// Model cho dashboard summary data
    /// </summary>
    public class DashboardSummary
    {
        public int TotalProduction { get; set; }
        public double QualityRate { get; set; }
        public double PlanAchievement { get; set; }
        public double UtilizationRate { get; set; }
        public int ActiveStations { get; set; }
        public int TotalStations { get; set; }
        public DateTime LastUpdated { get; set; }
    }
}
