using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using ZoomableApp.Models;
using ZoomableApp.PLC;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Mock PLC Service for testing and demo purposes
    /// Generates realistic fake data without requiring actual PLC connection
    /// </summary>
    public class MockPlcService : IPlcService
    {
        private readonly Random _random = new Random();
        private readonly Dictionary<PlcDeviceAddress, object> _mockData = new();
        private readonly string _plcId;
        private readonly System.Threading.Timer _dataUpdateTimer;
        private bool _isConnected = false;

        public bool IsConnected => _isConnected;
        public string PlcIpAddress { get; set; } = "127.0.0.1";
        public int PlcPort { get; set; } = 4999;

        public MockPlcService(string plcId = "MOCK_PLC")
        {
            _plcId = plcId;
            InitializeMockData();
            
            // Update mock data every 2 seconds to simulate real PLC behavior
            _dataUpdateTimer = new System.Threading.Timer(UpdateMockData, null, TimeSpan.Zero, TimeSpan.FromSeconds(2));
            
            Debug.WriteLine($"MockPlcService created for PLC ID: {_plcId}");
        }

        public Task<ConnectionResult> ConnectAsync()
        {
            // Simulate connection delay
            Task.Delay(500).Wait();
            
            _isConnected = true;
            Debug.WriteLine($"MockPlcService: Connected to {PlcIpAddress}:{PlcPort} (Simulated)");
            return Task.FromResult(new ConnectionResult(true, "Mock connection successful"));
        }

        public Task DisconnectAsync()
        {
            _isConnected = false;
            Debug.WriteLine($"MockPlcService: Disconnected from {PlcIpAddress}:{PlcPort} (Simulated)");
            return Task.CompletedTask;
        }

        public Task<PlcReadResult> ReadAsync(PlcDeviceAddress deviceAddress)
        {
            if (!_isConnected)
            {
                return Task.FromResult(PlcReadResult.Failure("Mock PLC not connected"));
            }

            try
            {
                var value = GetMockValue(deviceAddress);
                Debug.WriteLine($"MockPlcService: Read {deviceAddress} = {value}");
                return Task.FromResult(PlcReadResult.Success(value, "Mock read successful"));
            }
            catch (Exception ex)
            {
                return Task.FromResult(PlcReadResult.Failure($"Mock read error: {ex.Message}"));
            }
        }

        public Task<Dictionary<PlcDeviceAddress, object>> ReadMultipleAsync(IEnumerable<PlcDeviceAddress> deviceAddresses)
        {
            var results = new Dictionary<PlcDeviceAddress, object>();
            
            if (!_isConnected)
            {
                return Task.FromResult(results);
            }

            foreach (var address in deviceAddresses)
            {
                results[address] = GetMockValue(address);
            }

            return Task.FromResult(results);
        }

        public Task<PlcWriteResult> WriteAsync(PlcDeviceAddress deviceAddress, object value)
        {
            if (!_isConnected)
            {
                return Task.FromResult(PlcWriteResult.Failure("Mock PLC not connected"));
            }

            try
            {
                _mockData[deviceAddress] = value;
                Debug.WriteLine($"MockPlcService: Write {deviceAddress} = {value} (Simulated)");
                return Task.FromResult(PlcWriteResult.Success("Mock write successful"));
            }
            catch (Exception ex)
            {
                return Task.FromResult(PlcWriteResult.Failure($"Mock write error: {ex.Message}"));
            }
        }

        public Task<bool> WriteMultipleAsync(Dictionary<PlcDeviceAddress, object> valuesToWrite)
        {
            if (!_isConnected)
            {
                return Task.FromResult(false);
            }

            foreach (var kvp in valuesToWrite)
            {
                _mockData[kvp.Key] = kvp.Value;
            }

            return Task.FromResult(true);
        }

        public PlcRegisterInfo GetRegisterInfo(PlcDeviceAddress deviceAddress)
        {
            // Return mock register info based on device address
            return deviceAddress switch
            {
                PlcDeviceAddress.SystemReadyBit => new PlcRegisterInfo(deviceAddress, "M0", PlcDataType.BIT),
                PlcDeviceAddress.Product_OK => new PlcRegisterInfo(deviceAddress, "D1000", PlcDataType.WORD),
                PlcDeviceAddress.Product_NG => new PlcRegisterInfo(deviceAddress, "D1002", PlcDataType.WORD),
                PlcDeviceAddress.Product_Total => new PlcRegisterInfo(deviceAddress, "D1004", PlcDataType.WORD),
                PlcDeviceAddress.ProductCodeAtReader => new PlcRegisterInfo(deviceAddress, "D4000", PlcDataType.STRING, 10),
                PlcDeviceAddress.SystemRunningStatus => new PlcRegisterInfo(deviceAddress, "M10", PlcDataType.BIT),
                PlcDeviceAddress.SystemTotalOK => new PlcRegisterInfo(deviceAddress, "D2000", PlcDataType.WORD),
                PlcDeviceAddress.SystemTotalNG => new PlcRegisterInfo(deviceAddress, "D2002", PlcDataType.WORD),
                PlcDeviceAddress.MainlineFaultCode => new PlcRegisterInfo(deviceAddress, "D2010", PlcDataType.STRING),
                PlcDeviceAddress.InspectionFaultCode => new PlcRegisterInfo(deviceAddress, "D2020", PlcDataType.STRING),
                _ => new PlcRegisterInfo(deviceAddress, "D0", PlcDataType.WORD)
            };
        }

        private void InitializeMockData()
        {
            // Initialize with realistic starting values
            _mockData[PlcDeviceAddress.SystemReadyBit] = true;
            _mockData[PlcDeviceAddress.Product_OK] = 85;
            _mockData[PlcDeviceAddress.Product_NG] = 12;
            _mockData[PlcDeviceAddress.Product_Total] = 100;
            _mockData[PlcDeviceAddress.ProductCodeAtReader] = "PROD-001";
            
            // Initialize time-related data
            _mockData[PlcDeviceAddress.Time_Complete_ST1] = 45;
            _mockData[PlcDeviceAddress.Time_Complete_ST2] = 52;
            _mockData[PlcDeviceAddress.Time_Delay_ST1] = 3;
            _mockData[PlcDeviceAddress.Time_Stop_ST1] = 8;
            
            // Initialize status bits (using available enums)
            _mockData[PlcDeviceAddress.SystemReadyBit] = true;
            _mockData[PlcDeviceAddress.EmergencyStopBit] = false;

            // Initialize system status for Layout Controls
            _mockData[PlcDeviceAddress.SystemRunningStatus] = true;
            _mockData[PlcDeviceAddress.SystemTotalOK] = 150;
            _mockData[PlcDeviceAddress.SystemTotalNG] = 25;

            // Initialize fault codes (empty = no fault)
            _mockData[PlcDeviceAddress.MainlineFaultCode] = "";
            _mockData[PlcDeviceAddress.InspectionFaultCode] = "";
        }

        private object GetMockValue(PlcDeviceAddress deviceAddress)
        {
            if (_mockData.ContainsKey(deviceAddress))
            {
                return _mockData[deviceAddress];
            }

            // Generate realistic mock data based on device type
            return deviceAddress switch
            {
                // Production counters
                PlcDeviceAddress.Product_OK => _random.Next(80, 120),
                PlcDeviceAddress.Product_NG => _random.Next(5, 15),
                PlcDeviceAddress.Product_Total => _random.Next(95, 135),
                
                // System status bits
                PlcDeviceAddress.SystemReadyBit => true,
                PlcDeviceAddress.SystemErrorBit => _random.NextDouble() < 0.05, // 5% error
                PlcDeviceAddress.EmergencyStopBit => _random.NextDouble() < 0.02, // 2% emergency stop
                PlcDeviceAddress.SystemRunningStatus => _random.NextDouble() > 0.2, // 80% running

                // System totals for Layout Controls
                PlcDeviceAddress.SystemTotalOK => _random.Next(100, 300),
                PlcDeviceAddress.SystemTotalNG => _random.Next(10, 50),

                // Fault codes (simulate occasional faults)
                PlcDeviceAddress.MainlineFaultCode => GenerateRandomFaultCode("mainline"),
                PlcDeviceAddress.InspectionFaultCode => GenerateRandomFaultCode("inspection"),
                
                // Time values (in seconds)
                var addr when addr.ToString().Contains("Time_Complete") => _random.Next(30, 120),
                var addr when addr.ToString().Contains("Time_Delay") => _random.Next(1, 10),
                var addr when addr.ToString().Contains("Time_Stop") => _random.Next(0, 20),
                
                // Product codes
                PlcDeviceAddress.ProductCodeAtReader => $"PROD-{_random.Next(1, 999):D3}",
                
                // Default values
                var addr when addr.ToString().Contains("Bit") => _random.NextDouble() > 0.5,
                _ => _random.Next(0, 100)
            };
        }

        private void UpdateMockData(object state)
        {
            if (!_isConnected) return;

            try
            {
                // Simulate production progress
                if (_mockData.ContainsKey(PlcDeviceAddress.Product_OK))
                {
                    var currentOk = (int)_mockData[PlcDeviceAddress.Product_OK];
                    var currentNg = (int)_mockData[PlcDeviceAddress.Product_NG];
                    
                    // Randomly increment production
                    if (_random.NextDouble() < 0.3) // 30% chance to produce
                    {
                        if (_random.NextDouble() < 0.9) // 90% OK rate
                        {
                            _mockData[PlcDeviceAddress.Product_OK] = currentOk + 1;
                        }
                        else
                        {
                            _mockData[PlcDeviceAddress.Product_NG] = currentNg + 1;
                        }
                        
                        _mockData[PlcDeviceAddress.Product_Total] = currentOk + currentNg + 1;
                    }
                }

                // Simulate time variations
                foreach (var timeAddress in _mockData.Keys.Where(k => k.ToString().Contains("Time_")).ToList())
                {
                    var currentValue = (int)_mockData[timeAddress];
                    var variation = _random.Next(-2, 3); // ±2 variation
                    _mockData[timeAddress] = Math.Max(0, currentValue + variation);
                }

                // Occasionally change system status
                if (_random.NextDouble() < 0.05) // 5% chance
                {
                    _mockData[PlcDeviceAddress.SystemReadyBit] = !((bool)_mockData[PlcDeviceAddress.SystemReadyBit]);
                    _mockData[PlcDeviceAddress.SystemErrorBit] = !((bool)_mockData[PlcDeviceAddress.SystemReadyBit]);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"MockPlcService: Error updating mock data: {ex.Message}");
            }
        }

        /// <summary>
        /// Generate random fault codes for testing
        /// </summary>
        private string GenerateRandomFaultCode(string systemType)
        {
            // 90% chance of no fault
            if (_random.NextDouble() < 0.9)
                return "";

            // 10% chance of fault - select from predefined fault codes
            var faultCodes = systemType.ToLower() switch
            {
                "mainline" => new[] { "01", "02", "03", "M001", "M002", "S001" },
                "inspection" => new[] { "E101", "E102", "E103", "Z1", "Z2", "Z3" },
                _ => new[] { "01", "E101", "Z3" }
            };

            return faultCodes[_random.Next(faultCodes.Length)];
        }

        public void Dispose()
        {
            _dataUpdateTimer?.Dispose();
            Debug.WriteLine($"MockPlcService disposed for PLC ID: {_plcId}");
        }
    }
}
