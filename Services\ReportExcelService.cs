using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using ClosedXML.Excel;
using ZoomableApp.Models;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Service xuất báo cáo Excel
    /// </summary>
    public class ReportExcelService : BaseExcelService
    {
        private readonly string _exportPath;

        public ReportExcelService()
        {
            // Lấy đường dẫn từ config, mặc định là E:\Project-Dat\PANA
            var config = ConfigLoader.LoadExcelSettings();
            _exportPath = Path.GetDirectoryName(config.PlanFilePath) ?? @"E:\Project-Dat\PANA";

            // Tạo thư mục nếu chưa tồn tại
            EnsureDirectoryExists(Path.Combine(_exportPath, "dummy.xlsx"));
        }

        /// <summary>
        /// Xuất báo cáo sản lượng
        /// </summary>
        public string ExportProductionReport(List<ProductionData> data, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var fileName = GenerateTimestampedFileName("SanLuong");
                var filePath = Path.Combine(_exportPath, fileName);

                using var workbook = CreateWorkbook();
                var worksheet = GetOrCreateWorksheet(workbook, "Báo cáo sản lượng");

                // Header thông tin
                var titleRange = worksheet.Range(1, 1, 1, 12);
                titleRange.Cell(1, 1).Value = "BÁO CÁO SẢN LƯỢNG";
                ApplyTitleFormatting(titleRange);

                worksheet.Cell(2, 1).Value = $"Thời gian: {fromDate?.ToString("dd/MM/yyyy") ?? "Tất cả"} - {toDate?.ToString("dd/MM/yyyy") ?? "Tất cả"}";
                worksheet.Cell(3, 1).Value = $"Xuất lúc: {DateTime.Now:dd/MM/yyyy HH:mm:ss}";

                // Headers
                var headers = new[]
                {
                    "STT", "Thời gian", "Trạm", "Sản phẩm OK", "Sản phẩm NG",
                    "Tổng sản phẩm", "Thời gian hoàn thành", "Thời gian trễ",
                    "Thời gian dừng", "Số lần dừng", "Ca làm việc", "Ghi chú"
                };

                for (int i = 0; i < headers.Length; i++)
                {
                    var cell = worksheet.Cell(5, i + 1);
                    cell.Value = headers[i];
                    ApplyHeaderFormatting(cell);
                }

                // Data
                for (int i = 0; i < data.Count; i++)
                {
                    var row = i + 6;
                    var item = data[i];

                    worksheet.Cell(row, 1).Value = i + 1;
                    worksheet.Cell(row, 2).Value = item.Timestamp;
                    worksheet.Cell(row, 3).Value = item.Station;
                    worksheet.Cell(row, 4).Value = item.Product_OK;
                    worksheet.Cell(row, 5).Value = item.Product_NG;
                    worksheet.Cell(row, 6).Value = item.Product_Total;
                    worksheet.Cell(row, 7).Value = item.Time_Complete;
                    worksheet.Cell(row, 8).Value = item.Time_Delay;
                    worksheet.Cell(row, 9).Value = item.Time_Stop;
                    worksheet.Cell(row, 10).Value = item.Number_Stop;
                    worksheet.Cell(row, 11).Value = item.WorkShift;
                    worksheet.Cell(row, 12).Value = item.Notes;
                }

                // Auto-fit columns
                AutoFitColumns(worksheet);

                // Borders
                var dataRange = worksheet.Range(5, 1, 5 + data.Count, headers.Length);
                dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

                SaveWorkbook(workbook, filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error exporting production report: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Xuất báo cáo thao tác chậm
        /// </summary>
        public string ExportSlowOperationReport(List<ProductionData> data)
        {
            try
            {
                var fileName = GenerateTimestampedFileName("ThaoTacCham");
                var filePath = Path.Combine(_exportPath, fileName);

                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("Thao tác chậm");

                // Header
                worksheet.Cell(1, 1).Value = "BÁO CÁO THAO TÁC CHẬM";
                worksheet.Cell(1, 1).Style.Font.Bold = true;
                worksheet.Cell(1, 1).Style.Font.FontSize = 16;
                worksheet.Range(1, 1, 1, 8).Merge();

                worksheet.Cell(2, 1).Value = $"Xuất lúc: {DateTime.Now:dd/MM/yyyy HH:mm:ss}";

                // Headers
                var headers = new[] { "STT", "Thời gian", "Trạm", "Thời gian hoàn thành", "Thời gian trễ", "Ca làm việc", "Mã lỗi", "Ghi chú" };
                
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cell(4, i + 1).Value = headers[i];
                    worksheet.Cell(4, i + 1).Style.Font.Bold = true;
                    worksheet.Cell(4, i + 1).Style.Fill.BackgroundColor = XLColor.Orange;
                }

                // Data - chỉ hiển thị các trạm có thao tác chậm
                var slowData = data.Where(d => d.Time_Delay > 0 || d.Time_Complete > 10).ToList();
                
                for (int i = 0; i < slowData.Count; i++)
                {
                    var row = i + 5;
                    var item = slowData[i];

                    worksheet.Cell(row, 1).Value = i + 1;
                    worksheet.Cell(row, 2).Value = item.Timestamp;
                    worksheet.Cell(row, 3).Value = item.Station;
                    worksheet.Cell(row, 4).Value = item.Time_Complete;
                    worksheet.Cell(row, 5).Value = item.Time_Delay;
                    worksheet.Cell(row, 6).Value = item.WorkShift;
                    worksheet.Cell(row, 7).Value = item.Error_Code;
                    worksheet.Cell(row, 8).Value = item.Notes;
                }

                worksheet.ColumnsUsed().AdjustToContents();
                
                var dataRange = worksheet.Range(4, 1, 4 + slowData.Count, headers.Length);
                dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

                workbook.SaveAs(filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error exporting slow operation report: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Xuất báo cáo đo thao tác
        /// </summary>
        public string ExportMeasureOperationReport(List<ProductionData> data)
        {
            try
            {
                var fileName = GenerateTimestampedFileName("DoThaoTac");
                var filePath = Path.Combine(_exportPath, fileName);

                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("Đo thao tác");

                // Header
                worksheet.Cell(1, 1).Value = "BÁO CÁO ĐO THAO TÁC";
                worksheet.Cell(1, 1).Style.Font.Bold = true;
                worksheet.Cell(1, 1).Style.Font.FontSize = 16;
                worksheet.Range(1, 1, 1, 10).Merge();

                worksheet.Cell(2, 1).Value = $"Xuất lúc: {DateTime.Now:dd/MM/yyyy HH:mm:ss}";
                worksheet.Cell(3, 1).Value = "Đo 10 lần liên tiếp cho 18 vị trí làm việc";

                // Headers
                var headers = new[] { "STT", "Thời gian", "Trạm", "Lần đo", "Thời gian hoàn thành", "Thời gian trễ", "Số lần dừng", "Ca làm việc", "Mã lỗi", "Ghi chú" };
                
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cell(5, i + 1).Value = headers[i];
                    worksheet.Cell(5, i + 1).Style.Font.Bold = true;
                    worksheet.Cell(5, i + 1).Style.Fill.BackgroundColor = XLColor.LightBlue;
                }

                // Data
                for (int i = 0; i < data.Count; i++)
                {
                    var row = i + 6;
                    var item = data[i];

                    worksheet.Cell(row, 1).Value = i + 1;
                    worksheet.Cell(row, 2).Value = item.Timestamp;
                    worksheet.Cell(row, 3).Value = item.Station;
                    worksheet.Cell(row, 4).Value = (i % 10) + 1; // Lần đo (1-10)
                    worksheet.Cell(row, 5).Value = item.Time_Complete;
                    worksheet.Cell(row, 6).Value = item.Time_Delay;
                    worksheet.Cell(row, 7).Value = item.Number_Stop;
                    worksheet.Cell(row, 8).Value = item.WorkShift;
                    worksheet.Cell(row, 9).Value = item.Error_Code;
                    worksheet.Cell(row, 10).Value = item.Notes;
                }

                worksheet.ColumnsUsed().AdjustToContents();
                
                var dataRange = worksheet.Range(5, 1, 5 + data.Count, headers.Length);
                dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

                workbook.SaveAs(filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error exporting measure operation report: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Xuất báo cáo tổng hợp tháng
        /// </summary>
        public string ExportMonthlyReport(List<ProductionData> data, int month, int year)
        {
            try
            {
                var fileName = GenerateTimestampedFileName("TongHopThang");
                var filePath = Path.Combine(_exportPath, fileName);

                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("Tổng hợp tháng");

                // Header
                worksheet.Cell(1, 1).Value = $"BÁO CÁO TỔNG HỢP THÁNG {month}/{year}";
                worksheet.Cell(1, 1).Style.Font.Bold = true;
                worksheet.Cell(1, 1).Style.Font.FontSize = 16;
                worksheet.Range(1, 1, 1, 10).Merge();

                worksheet.Cell(2, 1).Value = $"Xuất lúc: {DateTime.Now:dd/MM/yyyy HH:mm:ss}";

                // Tổng hợp theo ca
                var shiftSummary = data.GroupBy(d => d.WorkShift)
                    .Select(g => new
                    {
                        Shift = g.Key,
                        TotalOK = g.Sum(x => x.Product_OK),
                        TotalNG = g.Sum(x => x.Product_NG),
                        TotalProducts = g.Sum(x => x.Product_Total),
                        AvgCompleteTime = g.Average(x => x.Time_Complete),
                        TotalDelayTime = g.Sum(x => x.Time_Delay),
                        TotalStopTime = g.Sum(x => x.Time_Stop),
                        TotalStops = g.Sum(x => x.Number_Stop)
                    }).ToList();

                // Headers tổng hợp
                var summaryHeaders = new[] { "Ca làm việc", "Sản phẩm OK", "Sản phẩm NG", "Tổng sản phẩm", "TG hoàn thành TB", "Tổng TG trễ", "Tổng TG dừng", "Tổng số lần dừng" };
                
                for (int i = 0; i < summaryHeaders.Length; i++)
                {
                    worksheet.Cell(4, i + 1).Value = summaryHeaders[i];
                    worksheet.Cell(4, i + 1).Style.Font.Bold = true;
                    worksheet.Cell(4, i + 1).Style.Fill.BackgroundColor = XLColor.LightGreen;
                }

                // Data tổng hợp
                for (int i = 0; i < shiftSummary.Count; i++)
                {
                    var row = i + 5;
                    var item = shiftSummary[i];

                    worksheet.Cell(row, 1).Value = item.Shift;
                    worksheet.Cell(row, 2).Value = item.TotalOK;
                    worksheet.Cell(row, 3).Value = item.TotalNG;
                    worksheet.Cell(row, 4).Value = item.TotalProducts;
                    worksheet.Cell(row, 5).Value = Math.Round(item.AvgCompleteTime, 2);
                    worksheet.Cell(row, 6).Value = item.TotalDelayTime;
                    worksheet.Cell(row, 7).Value = item.TotalStopTime;
                    worksheet.Cell(row, 8).Value = item.TotalStops;
                }

                worksheet.ColumnsUsed().AdjustToContents();
                
                var summaryRange = worksheet.Range(4, 1, 4 + shiftSummary.Count, summaryHeaders.Length);
                summaryRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                summaryRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

                workbook.SaveAs(filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error exporting monthly report: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Xuất báo cáo lịch sử lỗi
        /// </summary>
        public string ExportErrorHistoryReport(List<ProductionData> data)
        {
            try
            {
                var fileName = GenerateTimestampedFileName("LichSuLoi");
                var filePath = Path.Combine(_exportPath, fileName);

                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("Lịch sử lỗi");

                // Header
                worksheet.Cell(1, 1).Value = "BÁO CÁO LỊCH SỬ LỖI";
                worksheet.Cell(1, 1).Style.Font.Bold = true;
                worksheet.Cell(1, 1).Style.Font.FontSize = 16;
                worksheet.Range(1, 1, 1, 8).Merge();

                worksheet.Cell(2, 1).Value = $"Xuất lúc: {DateTime.Now:dd/MM/yyyy HH:mm:ss}";

                // Headers
                var headers = new[] { "STT", "Thời gian", "Trạm", "Mã lỗi", "Mô tả lỗi", "Ca làm việc", "Người tạo", "Ghi chú" };
                
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cell(4, i + 1).Value = headers[i];
                    worksheet.Cell(4, i + 1).Style.Font.Bold = true;
                    worksheet.Cell(4, i + 1).Style.Fill.BackgroundColor = XLColor.LightCoral;
                }

                // Data - chỉ hiển thị các bản ghi có lỗi
                var errorData = data.Where(d => !string.IsNullOrEmpty(d.Error_Code)).ToList();
                
                for (int i = 0; i < errorData.Count; i++)
                {
                    var row = i + 5;
                    var item = errorData[i];

                    worksheet.Cell(row, 1).Value = i + 1;
                    worksheet.Cell(row, 2).Value = item.Timestamp;
                    worksheet.Cell(row, 3).Value = item.Station;
                    worksheet.Cell(row, 4).Value = item.Error_Code;
                    worksheet.Cell(row, 5).Value = item.Error_Text;
                    worksheet.Cell(row, 6).Value = item.WorkShift;
                    worksheet.Cell(row, 7).Value = item.CreatedBy;
                    worksheet.Cell(row, 8).Value = item.Notes;
                }

                worksheet.ColumnsUsed().AdjustToContents();
                
                var dataRange = worksheet.Range(4, 1, 4 + errorData.Count, headers.Length);
                dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

                workbook.SaveAs(filePath);
                return filePath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error exporting error history report: {ex.Message}");
                throw;
            }
        }


    }
}
