using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using ZoomableApp.Models;

namespace ZoomableApp.Services
{
    public class DashboardDataService
    {
        private readonly string _connectionString;
        private readonly string _planFilePath;
        private IdleTimeExcelService _idleTimeExcelService;

        public DashboardDataService()
        {
            _connectionString = "Data Source=Data/panaDB.db;";
            _planFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "plan.xlsx");

            // Load idle time Excel file path from settings
            var excelSettings = ConfigLoader.LoadExcelSettings();
            var idleFilePath = !string.IsNullOrEmpty(excelSettings.IdlePlansFilePath)
                ? excelSettings.IdlePlansFilePath
                : "C:\\Project-Dat\\PANA\\idleplans.xlsx";

            // Note: If idle file doesn't exist, IdleTimeExcelService will handle it gracefully

            _idleTimeExcelService = new IdleTimeExcelService(idleFilePath);
        }

        /// <summary>
        /// Lấy dữ liệu idle time từ Excel và database
        /// </summary>
        public async Task<IdleTimeData> GetIdleTimeDataAsync()
        {
            try
            {
                // Đọc allowed idle time từ Excel
                var idleTimeData = _idleTimeExcelService.ReadIdleTimeData();

                // Lấy used idle time từ database
                idleTimeData.DailyUsedIdleTime = await GetUsedIdleTimeAsync(DateTime.Today, DateTime.Today.AddDays(1));

                var currentMonth = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                var nextMonth = currentMonth.AddMonths(1);
                idleTimeData.MonthlyUsedIdleTime = await GetUsedIdleTimeAsync(currentMonth, nextMonth);

                return idleTimeData;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error reading idle time data: {ex.Message}");
                return new IdleTimeData
                {
                    DailyAllowedIdleTime = TimeSpan.FromHours(2),
                    MonthlyAllowedIdleTime = TimeSpan.FromHours(40),
                    DailyUsedIdleTime = TimeSpan.FromHours(1.1),
                    MonthlyUsedIdleTime = TimeSpan.FromHours(27)
                };
            }
        }

        /// <summary>
        /// Lấy dữ liệu sản xuất tổng hợp theo ngày
        /// </summary>
        public async Task<ProductionSummaryData> GetDailyProductionSummaryAsync(DateTime date)
        {
            try
            {
                var startDate = date.Date;
                var endDate = date.Date.AddDays(1);

                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT 
                        SUM(Product_OK) as TotalOK,
                        SUM(Product_NG) as TotalNG,
                        SUM(Product_Total) as TotalProduced
                    FROM ProductionData 
                    WHERE datetime(Timestamp) >= @StartDate 
                    AND datetime(Timestamp) < @EndDate";

                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd HH:mm:ss"));

                using var reader = await command.ExecuteReaderAsync();
                
                var summary = new ProductionSummaryData();
                
                if (await reader.ReadAsync())
                {
                    summary.OkQuantity = reader.IsDBNull("TotalOK") ? 0 : reader.GetInt32("TotalOK");
                    summary.NgQuantity = reader.IsDBNull("TotalNG") ? 0 : reader.GetInt32("TotalNG");
                    var totalProduced = reader.IsDBNull("TotalProduced") ? 0 : reader.GetInt32("TotalProduced");
                    
                    // Rework = Total - OK - NG
                    summary.ReworkQuantity = Math.Max(0, totalProduced - summary.OkQuantity - summary.NgQuantity);
                }

                // Lấy plan quantity từ Excel
                summary.PlanQuantity = await GetPlanQuantityAsync(date);

                return summary;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting daily production summary: {ex.Message}");
                return new ProductionSummaryData();
            }
        }

        /// <summary>
        /// Lấy dữ liệu sản xuất theo ca
        /// </summary>
        public async Task<ProductionSummaryData> GetShiftProductionSummaryAsync(DateTime date, string shiftName)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT 
                        SUM(Product_OK) as TotalOK,
                        SUM(Product_NG) as TotalNG,
                        SUM(Product_Total) as TotalProduced
                    FROM ProductionData 
                    WHERE DATE(Timestamp) = @Date 
                    AND WorkShift = @ShiftName";

                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@Date", date.ToString("yyyy-MM-dd"));
                command.Parameters.AddWithValue("@ShiftName", shiftName);

                using var reader = await command.ExecuteReaderAsync();
                
                var summary = new ProductionSummaryData();
                
                if (await reader.ReadAsync())
                {
                    summary.OkQuantity = reader.IsDBNull("TotalOK") ? 0 : reader.GetInt32("TotalOK");
                    summary.NgQuantity = reader.IsDBNull("TotalNG") ? 0 : reader.GetInt32("TotalNG");
                    var totalProduced = reader.IsDBNull("TotalProduced") ? 0 : reader.GetInt32("TotalProduced");
                    
                    summary.ReworkQuantity = Math.Max(0, totalProduced - summary.OkQuantity - summary.NgQuantity);
                }

                // Lấy plan quantity cho ca này
                summary.PlanQuantity = await GetShiftPlanQuantityAsync(date, shiftName);

                return summary;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting shift production summary: {ex.Message}");
                return new ProductionSummaryData();
            }
        }

        private TimeSpan GetIdleTimeFromWorksheet(object worksheet, string type)
        {
            // Tạm thời return giá trị mặc định, sẽ implement Excel reading sau
            return type == "Daily" ? TimeSpan.FromHours(2) : TimeSpan.FromHours(40);
        }

        private async Task<TimeSpan> GetUsedIdleTimeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT SUM(Time_Stop) as TotalIdleTime
                    FROM ProductionData 
                    WHERE datetime(Timestamp) >= @StartDate 
                    AND datetime(Timestamp) < @EndDate";

                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd HH:mm:ss"));

                var result = await command.ExecuteScalarAsync();
                if (result != null && result != DBNull.Value)
                {
                    var totalMinutes = Convert.ToDouble(result);
                    return TimeSpan.FromMinutes(totalMinutes);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting used idle time: {ex.Message}");
            }

            return TimeSpan.Zero;
        }

        private async Task<int> GetPlanQuantityAsync(DateTime date)
        {
            // Tạm thời return giá trị mẫu, sẽ implement Excel reading sau
            await Task.Delay(1);
            return 100; // Giá trị mẫu
        }

        private async Task<int> GetShiftPlanQuantityAsync(DateTime date, string shiftName)
        {
            // Tạm thời chia đều plan quantity cho các ca
            var dailyPlan = await GetPlanQuantityAsync(date);
            return dailyPlan / 3; // Giả sử có 3 ca
        }
    }
}
