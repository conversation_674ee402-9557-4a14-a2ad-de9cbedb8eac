using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using System.Windows.Controls;
using ZoomableApp.ViewModels;

namespace ZoomableApp.Views
{
    /// <summary>
    /// Interaction logic for ShiftPlanActualChartControl.xaml
    /// </summary>
    public partial class ShiftPlanActualChartControl : UserControl
    {
        private ShiftPlanActualChartViewModel _viewModel;

        public ShiftPlanActualChartControl()
        {
            InitializeComponent();
            ShiftPlanChart.LegendTextPaint = new SolidColorPaint(SKColors.White);
            _viewModel = new ShiftPlanActualChartViewModel();
            DataContext = _viewModel;
        }
    }
}
