using System;
using System.Collections.Generic;
using System.Diagnostics;
using ZoomableApp.Models;
using ZoomableApp.PLC;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Simple dependency injection container for managing service instances
    /// Supports both singleton and transient service lifetimes
    /// </summary>
    public static class ServiceContainer
    {
        private static readonly Dictionary<Type, object> _singletonServices = new();
        private static readonly Dictionary<Type, Func<object>> _transientFactories = new();
        private static bool _isInitialized = false;

        /// <summary>
        /// Initializes the service container with the specified PLC mode
        /// </summary>
        /// <param name="plcMode">PLC operating mode</param>
        public static void Initialize(PlcMode plcMode)
        {
            if (_isInitialized)
            {
                Debug.WriteLine("ServiceContainer: Already initialized, skipping...");
                return;
            }

            Debug.WriteLine($"ServiceContainer: Initializing with PLC mode: {plcMode}");

            try
            {
                // Clear existing services
                _singletonServices.Clear();
                _transientFactories.Clear();

                // Register core services (including logger)
                RegisterCoreServices(plcMode);

                // Get logger for subsequent logging
                var logger = GetService<ILoggerService>();

                // Register PLC-related services
                RegisterPlcServices(plcMode);

                // Register dashboard services
                RegisterDashboardServices();

                _isInitialized = true;
                logger.LogInfo("ServiceContainer: Initialization completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ServiceContainer: Initialization failed: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gets a singleton service instance
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <returns>Service instance</returns>
        public static T GetService<T>()
        {
            var serviceType = typeof(T);
            
            if (_singletonServices.TryGetValue(serviceType, out var service))
            {
                return (T)service;
            }

            if (_transientFactories.TryGetValue(serviceType, out var factory))
            {
                return (T)factory();
            }

            throw new InvalidOperationException($"Service of type {serviceType.Name} is not registered");
        }

        /// <summary>
        /// Checks if a service is registered
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <returns>True if service is registered</returns>
        public static bool IsRegistered<T>()
        {
            var serviceType = typeof(T);
            return _singletonServices.ContainsKey(serviceType) || _transientFactories.ContainsKey(serviceType);
        }

        /// <summary>
        /// Registers a singleton service instance
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <param name="instance">Service instance</param>
        public static void RegisterSingleton<T>(T instance)
        {
            _singletonServices[typeof(T)] = instance;
            var message = $"ServiceContainer: Registered singleton {typeof(T).Name}";
            Debug.WriteLine(message);

            // Try to log to file if logger is available
            if (_singletonServices.TryGetValue(typeof(ILoggerService), out var loggerObj) && loggerObj is ILoggerService logger)
            {
                logger.LogDebug(message);
            }
        }

        /// <summary>
        /// Registers a transient service factory
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <param name="factory">Factory function</param>
        public static void RegisterTransient<T>(Func<T> factory)
        {
            _transientFactories[typeof(T)] = () => factory();
            var message = $"ServiceContainer: Registered transient {typeof(T).Name}";
            Debug.WriteLine(message);

            // Try to log to file if logger is available
            if (_singletonServices.TryGetValue(typeof(ILoggerService), out var loggerObj) && loggerObj is ILoggerService logger)
            {
                logger.LogDebug(message);
            }
        }

        /// <summary>
        /// Resets the container and clears all registrations
        /// </summary>
        public static void Reset()
        {
            // Log before clearing if logger is available
            if (_singletonServices.TryGetValue(typeof(ILoggerService), out var loggerObj) && loggerObj is ILoggerService logger)
            {
                logger.LogInfo("ServiceContainer: Reset completed");
            }

            _singletonServices.Clear();
            _transientFactories.Clear();
            _isInitialized = false;
            Debug.WriteLine("ServiceContainer: Reset completed");
        }

        private static void RegisterCoreServices(PlcMode plcMode)
        {
            // Register logging service first
            var logger = new FileLoggerService();
            RegisterSingleton<ILoggerService>(logger);
            logger.LogInfo("ServiceContainer: Logging service initialized");

            // Register configuration-related services
            RegisterSingleton<PlcMode>(plcMode);

            // Register PLC connection manager (will be updated to use factory)
            var plcConfigs = ConfigLoader.LoadPlcConfigs();
            var plcManager = new PlcConnectionManager(plcConfigs);
            RegisterSingleton<PlcConnectionManager>(plcManager);

            // Register PLC fault service
            var faultService = new PlcFaultService();
            RegisterSingleton<PlcFaultService>(faultService);

            var canvasFaultDisplayService = new CanvasFaultDisplayService();
            RegisterSingleton<CanvasFaultDisplayService>(canvasFaultDisplayService);
        }

        private static void RegisterPlcServices(PlcMode plcMode)
        {
            // Register PLC service factory as transient
            RegisterTransient<IPlcService>(() => 
            {
                // This will be called each time a new PLC service is needed
                // In practice, you might want to pass specific PLC ID
                return PlcServiceFactory.CreatePlcService("DEFAULT_PLC", plcMode);
            });

            // Register production data service
            var plcManager = GetService<PlcConnectionManager>();
            var productionDataService = new ProductionDataService(plcManager);
            RegisterSingleton<ProductionDataService>(productionDataService);
        }

        private static void RegisterDashboardServices()
        {
            // Register dashboard data service
            var dashboardService = new DashboardDataService();
            RegisterSingleton<DashboardDataService>(dashboardService);

            // Register mock dashboard data service
            var mockDashboardService = new MockDashboardDataService();
            RegisterSingleton<MockDashboardDataService>(mockDashboardService);

            // Register other dashboard-related services as needed
        }

        /// <summary>
        /// Gets diagnostic information about registered services
        /// </summary>
        /// <returns>Dictionary of service types and their registration status</returns>
        public static Dictionary<string, string> GetDiagnosticInfo()
        {
            var info = new Dictionary<string, string>();
            
            foreach (var kvp in _singletonServices)
            {
                info[kvp.Key.Name] = "Singleton";
            }
            
            foreach (var kvp in _transientFactories)
            {
                info[kvp.Key.Name] = "Transient";
            }
            
            info["IsInitialized"] = _isInitialized.ToString();
            info["TotalServices"] = (_singletonServices.Count + _transientFactories.Count).ToString();
            
            return info;
        }
    }
}
