{"PLC_Real_Test": [{"LogicalName": "SystemReadyBit", "PhysicalAddress": "M0", "DataType": "BIT", "Length": 1}, {"LogicalName": "ProductCodeAtReader", "PhysicalAddress": "D4000", "DataType": "STRING", "Length": 10}, {"LogicalName": "SequenceForProductCode", "PhysicalAddress": "D100", "DataType": "WORD", "Length": 1}, {"LogicalName": "GenericTestDataWord", "PhysicalAddress": "D0", "DataType": "WORD", "Length": 1}], "PLC1_Reader": [{"LogicalName": "SystemReadyBit", "PhysicalAddress": "M0", "DataType": "BIT", "Length": 1}, {"LogicalName": "ProductCodeAtReader", "PhysicalAddress": "D4000", "DataType": "STRING", "Length": 10}, {"LogicalName": "SequenceForProductCode", "PhysicalAddress": "D100", "DataType": "WORD", "Length": 1}, {"LogicalName": "GenericTestDataWord", "PhysicalAddress": "D0", "DataType": "WORD", "Length": 1}], "PLC8_Tester": [{"LogicalName": "SystemReadyBit", "PhysicalAddress": "M1000", "DataType": "BIT", "Length": 1}, {"LogicalName": "TestResultFromTester", "PhysicalAddress": "D500", "DataType": "WORD", "Length": 1}, {"LogicalName": "SequenceNumberAtTest", "PhysicalAddress": "D502", "DataType": "WORD", "Length": 1}, {"LogicalName": "GenericTestDataWord", "PhysicalAddress": "D0", "DataType": "WORD", "Length": 1}], "PLC_GeneralStatus": [{"LogicalName": "OverallSystemError", "PhysicalAddress": "M2000", "DataType": "BIT", "Length": 1}, {"LogicalName": "EmergencyStopStatus", "PhysicalAddress": "X10", "DataType": "BIT", "Length": 1}], "PLC_Test_Machine_1": [{"LogicalName": "NumberProductOK", "PhysicalAddress": "D100", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberProductNG", "PhysicalAddress": "D200", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberProductTotal", "PhysicalAddress": "D24", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumProductPresent", "PhysicalAddress": "D26", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeModel", "PhysicalAddress": "D28", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCurrent", "PhysicalAddress": "D30", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelay", "PhysicalAddress": "D32", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeNextStep", "PhysicalAddress": "D34", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST1", "PhysicalAddress": "D200", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST2", "PhysicalAddress": "D202", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST3", "PhysicalAddress": "D204", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST4", "PhysicalAddress": "D206", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST5", "PhysicalAddress": "D208", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST6", "PhysicalAddress": "D210", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST7", "PhysicalAddress": "D212", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST8", "PhysicalAddress": "D214", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST9", "PhysicalAddress": "D216", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST10", "PhysicalAddress": "D218", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST11", "PhysicalAddress": "D220", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST12", "PhysicalAddress": "D222", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST13", "PhysicalAddress": "D224", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST14", "PhysicalAddress": "D226", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST15", "PhysicalAddress": "D228", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST16", "PhysicalAddress": "D230", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST17", "PhysicalAddress": "D232", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST18", "PhysicalAddress": "D234", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST19", "PhysicalAddress": "D236", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST20", "PhysicalAddress": "D238", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST21", "PhysicalAddress": "D240", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST22", "PhysicalAddress": "D242", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST23", "PhysicalAddress": "D244", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST24", "PhysicalAddress": "D246", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST25", "PhysicalAddress": "D248", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCompleteST26", "PhysicalAddress": "D250", "DataType": "WORD", "Length": 1}, {"LogicalName": "CheckProSt1_1", "PhysicalAddress": "D400", "DataType": "WORD", "Length": 1}, {"LogicalName": "CheckProSt1_2", "PhysicalAddress": "D401", "DataType": "WORD", "Length": 1}, {"LogicalName": "CheckProSt1_3", "PhysicalAddress": "D402", "DataType": "WORD", "Length": 1}, {"LogicalName": "CheckProSt1_4", "PhysicalAddress": "D403", "DataType": "WORD", "Length": 1}, {"LogicalName": "CheckProSt1_5", "PhysicalAddress": "D404", "DataType": "WORD", "Length": 1}, {"LogicalName": "CheckProSt2_1", "PhysicalAddress": "D405", "DataType": "WORD", "Length": 1}, {"LogicalName": "CheckProSt2_2", "PhysicalAddress": "D406", "DataType": "WORD", "Length": 1}, {"LogicalName": "CheckProSt2_3", "PhysicalAddress": "D407", "DataType": "WORD", "Length": 1}, {"LogicalName": "CheckProSt2_4", "PhysicalAddress": "D408", "DataType": "WORD", "Length": 1}, {"LogicalName": "CheckProSt2_5", "PhysicalAddress": "D409", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberProductOK_PC", "PhysicalAddress": "D1000", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberProductNG_PC", "PhysicalAddress": "D1002", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberProductTotal_PC", "PhysicalAddress": "D1004", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumProductPresent_PC", "PhysicalAddress": "D1006", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeModel_PC", "PhysicalAddress": "D1008", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeCurrent_PC", "PhysicalAddress": "D1010", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelay_PC", "PhysicalAddress": "D1012", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeNextStep_PC", "PhysicalAddress": "D1014", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST1", "PhysicalAddress": "D1080", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST2", "PhysicalAddress": "D1082", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST3", "PhysicalAddress": "D1084", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST4", "PhysicalAddress": "D1086", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST5", "PhysicalAddress": "D1088", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST6", "PhysicalAddress": "D1090", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST7", "PhysicalAddress": "D1092", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST8", "PhysicalAddress": "D1094", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST9", "PhysicalAddress": "D1096", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST10", "PhysicalAddress": "D1098", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST11", "PhysicalAddress": "D1100", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST12", "PhysicalAddress": "D1102", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST13", "PhysicalAddress": "D1104", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST14", "PhysicalAddress": "D1106", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST15", "PhysicalAddress": "D1108", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST16", "PhysicalAddress": "D1110", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST17", "PhysicalAddress": "D1112", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST18", "PhysicalAddress": "D1114", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST19", "PhysicalAddress": "D1116", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST20", "PhysicalAddress": "D1118", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST21", "PhysicalAddress": "D1120", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST22", "PhysicalAddress": "D1122", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST23", "PhysicalAddress": "D1124", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST24", "PhysicalAddress": "D1126", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST25", "PhysicalAddress": "D1128", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeDelayST26", "PhysicalAddress": "D1130", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST1", "PhysicalAddress": "D1132", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST2", "PhysicalAddress": "D1134", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST3", "PhysicalAddress": "D1136", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST4", "PhysicalAddress": "D1138", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST5", "PhysicalAddress": "D1140", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST6", "PhysicalAddress": "D1142", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST7", "PhysicalAddress": "D1144", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST8", "PhysicalAddress": "D1146", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST9", "PhysicalAddress": "D1148", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST10", "PhysicalAddress": "D1150", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST11", "PhysicalAddress": "D1152", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST12", "PhysicalAddress": "D1154", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST13", "PhysicalAddress": "D1156", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST14", "PhysicalAddress": "D1158", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST15", "PhysicalAddress": "D1160", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST16", "PhysicalAddress": "D1162", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST17", "PhysicalAddress": "D1164", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST18", "PhysicalAddress": "D1166", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST19", "PhysicalAddress": "D1168", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST20", "PhysicalAddress": "D1170", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST21", "PhysicalAddress": "D1172", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST22", "PhysicalAddress": "D1174", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST23", "PhysicalAddress": "D1176", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST24", "PhysicalAddress": "D1178", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST25", "PhysicalAddress": "D1180", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopST26", "PhysicalAddress": "D1182", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST1", "PhysicalAddress": "D1190", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST2", "PhysicalAddress": "D1191", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST3", "PhysicalAddress": "D1192", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST4", "PhysicalAddress": "D1193", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST5", "PhysicalAddress": "D1194", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST6", "PhysicalAddress": "D1195", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST7", "PhysicalAddress": "D1196", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST8", "PhysicalAddress": "D1197", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST9", "PhysicalAddress": "D1198", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST10", "PhysicalAddress": "D1199", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST11", "PhysicalAddress": "D1200", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST12", "PhysicalAddress": "D1201", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST13", "PhysicalAddress": "D1202", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST14", "PhysicalAddress": "D1203", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST15", "PhysicalAddress": "D1204", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST16", "PhysicalAddress": "D1205", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST17", "PhysicalAddress": "D1206", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST18", "PhysicalAddress": "D1207", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST19", "PhysicalAddress": "D1208", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST20", "PhysicalAddress": "D1209", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST21", "PhysicalAddress": "D1210", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST22", "PhysicalAddress": "D1211", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST23", "PhysicalAddress": "D1212", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST24", "PhysicalAddress": "D1213", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST25", "PhysicalAddress": "D1214", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberStopST26", "PhysicalAddress": "D1215", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeRunningHour", "PhysicalAddress": "D1400", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeRunningMinute", "PhysicalAddress": "D1402", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeRunningSecond", "PhysicalAddress": "D1404", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopHour", "PhysicalAddress": "D1406", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopMinute", "PhysicalAddress": "D1408", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopSecond", "PhysicalAddress": "D1410", "DataType": "WORD", "Length": 1}, {"LogicalName": "NotError", "PhysicalAddress": "M0", "DataType": "BIT", "Length": 1}, {"LogicalName": "AutoMode", "PhysicalAddress": "M1", "DataType": "BIT", "Length": 1}, {"LogicalName": "ManMode", "PhysicalAddress": "M2", "DataType": "BIT", "Length": 1}, {"LogicalName": "OriginMode", "PhysicalAddress": "M3", "DataType": "BIT", "Length": 1}, {"LogicalName": "HomeDone", "PhysicalAddress": "M4", "DataType": "BIT", "Length": 1}, {"LogicalName": "RunningMode", "PhysicalAddress": "M5", "DataType": "BIT", "Length": 1}, {"LogicalName": "StopMode", "PhysicalAddress": "M6", "DataType": "BIT", "Length": 1}, {"LogicalName": "ManualCylinder1Up", "PhysicalAddress": "M2000", "DataType": "BIT", "Length": 1}, {"LogicalName": "ManualCylinder1Down", "PhysicalAddress": "M2001", "DataType": "BIT", "Length": 1}, {"LogicalName": "ManualCylinder2Up", "PhysicalAddress": "M2002", "DataType": "BIT", "Length": 1}, {"LogicalName": "ManualCylinder2Down", "PhysicalAddress": "M2003", "DataType": "BIT", "Length": 1}, {"LogicalName": "ManualCylinder3Clamp", "PhysicalAddress": "M2004", "DataType": "BIT", "Length": 1}, {"LogicalName": "ManualCylinder3Release", "PhysicalAddress": "M2005", "DataType": "BIT", "Length": 1}, {"LogicalName": "ManualCylinder4Clamp", "PhysicalAddress": "M2006", "DataType": "BIT", "Length": 1}, {"LogicalName": "ManualCylinder4Release", "PhysicalAddress": "M2007", "DataType": "BIT", "Length": 1}, {"LogicalName": "ManualCylinder1Error", "PhysicalAddress": "M2008", "DataType": "BIT", "Length": 1}, {"LogicalName": "ManualCylinder2Error", "PhysicalAddress": "M2009", "DataType": "BIT", "Length": 1}, {"LogicalName": "ManualCylinder3Error", "PhysicalAddress": "M2010", "DataType": "BIT", "Length": 1}, {"LogicalName": "ManualCylinder4Error", "PhysicalAddress": "M2011", "DataType": "BIT", "Length": 1}, {"LogicalName": "ManualCylinder5Error", "PhysicalAddress": "M2012", "DataType": "BIT", "Length": 1}, {"LogicalName": "ManualCylinder6Error", "PhysicalAddress": "M2013", "DataType": "BIT", "Length": 1}, {"LogicalName": "ManualCylinderOK1", "PhysicalAddress": "M2014", "DataType": "BIT", "Length": 1}, {"LogicalName": "ManualCylinderOK2", "PhysicalAddress": "M2015", "DataType": "BIT", "Length": 1}, {"LogicalName": "Product_OK", "PhysicalAddress": "D1000", "DataType": "WORD", "Length": 1}, {"LogicalName": "Product_NG", "PhysicalAddress": "D1002", "DataType": "WORD", "Length": 1}, {"LogicalName": "Product_Total", "PhysicalAddress": "D1004", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST1", "PhysicalAddress": "D72", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST2", "PhysicalAddress": "D73", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST3", "PhysicalAddress": "D74", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST4", "PhysicalAddress": "D75", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST5", "PhysicalAddress": "D76", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST6", "PhysicalAddress": "D77", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST7", "PhysicalAddress": "D78", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST8", "PhysicalAddress": "D79", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST9", "PhysicalAddress": "D80", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST10", "PhysicalAddress": "D81", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST11", "PhysicalAddress": "D82", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST12", "PhysicalAddress": "D83", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST13", "PhysicalAddress": "D84", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST14", "PhysicalAddress": "D85", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST15", "PhysicalAddress": "D86", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST16", "PhysicalAddress": "D87", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST17", "PhysicalAddress": "D88", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Complete_ST18", "PhysicalAddress": "D89", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST1", "PhysicalAddress": "D1080", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST2", "PhysicalAddress": "D1082", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST3", "PhysicalAddress": "D1084", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST4", "PhysicalAddress": "D1086", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST5", "PhysicalAddress": "D1088", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST6", "PhysicalAddress": "D1090", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST7", "PhysicalAddress": "D1092", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST8", "PhysicalAddress": "D1094", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST9", "PhysicalAddress": "D1096", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST10", "PhysicalAddress": "D1098", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST11", "PhysicalAddress": "D1100", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST12", "PhysicalAddress": "D1102", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST13", "PhysicalAddress": "D1104", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST14", "PhysicalAddress": "D1106", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST15", "PhysicalAddress": "D1108", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST16", "PhysicalAddress": "D1110", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST17", "PhysicalAddress": "D1112", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Delay_ST18", "PhysicalAddress": "D1114", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST1", "PhysicalAddress": "D1132", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST2", "PhysicalAddress": "D1134", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST3", "PhysicalAddress": "D1136", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST4", "PhysicalAddress": "D1138", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST5", "PhysicalAddress": "D1140", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST6", "PhysicalAddress": "D1142", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST7", "PhysicalAddress": "D1144", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST8", "PhysicalAddress": "D1146", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST9", "PhysicalAddress": "D1148", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST10", "PhysicalAddress": "D1150", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST11", "PhysicalAddress": "D1152", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST12", "PhysicalAddress": "D1154", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST13", "PhysicalAddress": "D1156", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST14", "PhysicalAddress": "D1158", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST15", "PhysicalAddress": "D1160", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST16", "PhysicalAddress": "D1162", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST17", "PhysicalAddress": "D1164", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST18", "PhysicalAddress": "D1166", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST1", "PhysicalAddress": "D1190", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST2", "PhysicalAddress": "D1191", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST3", "PhysicalAddress": "D1192", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST4", "PhysicalAddress": "D1193", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST5", "PhysicalAddress": "D1194", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST6", "PhysicalAddress": "D1195", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST7", "PhysicalAddress": "D1196", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST8", "PhysicalAddress": "D1197", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST9", "PhysicalAddress": "D1198", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST10", "PhysicalAddress": "D1199", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST11", "PhysicalAddress": "D1200", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST12", "PhysicalAddress": "D1201", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST13", "PhysicalAddress": "D1202", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST14", "PhysicalAddress": "D1203", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST15", "PhysicalAddress": "D1204", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST16", "PhysicalAddress": "D1205", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST17", "PhysicalAddress": "D1206", "DataType": "WORD", "Length": 1}, {"LogicalName": "Number_Stop_ST18", "PhysicalAddress": "D1207", "DataType": "WORD", "Length": 1}, {"LogicalName": "Error_M0", "PhysicalAddress": "M0", "DataType": "BIT", "Length": 1}, {"LogicalName": "Error_X06", "PhysicalAddress": "X06", "DataType": "BIT", "Length": 1}, {"LogicalName": "Error_X1610", "PhysicalAddress": "X1610", "DataType": "BIT", "Length": 1}, {"LogicalName": "SystemRunningStatus", "PhysicalAddress": "M10", "DataType": "BIT", "Length": 1}, {"LogicalName": "SystemTotalOK", "PhysicalAddress": "D100", "DataType": "WORD", "Length": 1}, {"LogicalName": "SystemTotalNG", "PhysicalAddress": "D200", "DataType": "WORD", "Length": 1}, {"LogicalName": "MainlineFaultCode", "PhysicalAddress": "D2010", "DataType": "STRING", "Length": 10}, {"LogicalName": "InspectionFaultCode", "PhysicalAddress": "D2020", "DataType": "STRING", "Length": 10}]}