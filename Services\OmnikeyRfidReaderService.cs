using PCSC;
using PCSC.Monitoring;
using PCSC.Iso7816;

namespace ZoomableApp.Services
{
    /// <summary>
    /// D<PERSON><PERSON> vụ đọc thẻ RFID cho đầu đọc HID Omnikey 5422/5122 sử dụng PC/SC.
    /// </summary>
    public class OmnikeyRfidReaderService : IRfidReaderService
    {
        private readonly ISCardContext _context;
        private readonly ISCardMonitor _monitor;
        private bool _started;

        public event Action<string>? CardScanned;

        public OmnikeyRfidReaderService()
        {
            _context = ContextFactory.Instance.Establish(SCardScope.System);
            _monitor = MonitorFactory.Instance.Create(SCardScope.System);
            _monitor.CardInserted += OnCardInserted;
        }

        private void OnCardInserted(object? sender, CardStatusEventArgs args)
        {
            ReadCard(args.ReaderName);
        }

        private void ReadCard(string readerName)
        {
            try
            {
                using var isoReader = new IsoReader(_context, readerName, SCardShareMode.Shared, SCardProtocol.Any, false);
                var apdu = new CommandApdu(IsoCase.Case2Short, isoReader.ActiveProtocol)
                {
                    CLA = 0xFF,
                    INS = 0xCA,
                    P1 = 0x00,
                    P2 = 0x00,
                    Le = 0
                };
                var response = isoReader.Transmit(apdu);
                if (response.HasData)
                {
                    var uid = BitConverter.ToString(response.GetData()).Replace("-", string.Empty);
                    CardScanned?.Invoke(uid);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"RFID read error: {ex.Message}");
            }
        }

        public async Task StartListeningAsync()
        {
            if (_started) return;
            var readers = _context.GetReaders();
            if (readers is { Length: > 0 })
            {
                _monitor.Start(readers);
                _started = true;
            }
            await Task.CompletedTask;
        }

        public void StopListening()
        {
            if (_started)
            {
                _monitor.Cancel();
                _started = false;
            }
        }

        public void Dispose()
        {
            StopListening();
            _monitor.Dispose();
            _context.Dispose();
        }
    }
}
